chainMix 是一个基于 React Native + Expo 技术栈开发的跨平台移动应用，融合AI智能分析，为用户提供最新的区块链和加密货币资讯。当前目录 `/Users/<USER>/Desktop/conan-work/android-app1/chainmix` 是项目工程目录。

我有一个后端服务位于 `@server` 目录，基于 Node.js 开发，聚合多个第三方 API 作为统一的服务接口。

请帮我进行全面的前后端代码审查和集成分析，具体任务如下：

**第一阶段：架构和功能模块分析**
1. 分析前端页面结构和路由配置，识别所有现有页面模块
2. 分析后端 API 路由和端点，列出所有可用的服务接口
3. 评估前端页面模块的合理性和完整性：
   - 资讯页面（新闻聚合）
   - 快讯页面（实时动态）
   - 文章页面（深度内容）
   - 推特帖子页面（社交媒体内容）
   - AI助手页面（智能分析功能）
   - 发现页面（内容推荐）
   - 其他必要的功能页面
4. 识别缺失的关键功能模块并提出建议

**第二阶段：API 对接和兼容性检查**
0. 对接前后端 API
1. 核对前端 API 调用与后端端点的匹配情况
2. 验证 HTTP 请求方法（GET/POST/PUT/DELETE）的一致性
3. 检查服务端口配置和前端请求地址的正确性
4. 分析 API 响应格式和前端期望数据结构的兼容性

**第三阶段：数据字段对齐验证**
1. 对比前端组件中使用的数据字段与后端 API 返回的字段
2. 识别字段命名不一致、类型不匹配或缺失的情况
3. 检查数据转换和映射逻辑的正确性
4. 验证必填字段和可选字段的处理

**第四阶段：关键问题修复**
1. 识别并修复 P0 级别的阻塞性问题：
   - API 连接失败
   - 关键功能无法正常工作
   - 数据显示错误
   - 应用崩溃或严重性能问题
2. 提供具体的代码修复方案
3. 建议必要的新功能实现

请使用任务管理工具来组织这个复杂的多阶段工作，并在每个阶段完成后提供详细的分析报告和具体的改进建议。


----------------------------------------------------------------------------------
