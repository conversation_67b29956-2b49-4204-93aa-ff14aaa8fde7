import React, { useState, useEffect } from 'react';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';

import RootNavigator from './src/navigation';
import { useAppStore } from './src/store';
import { ThemeProvider } from './src/components/common/ThemeProvider';
import ErrorBoundary from './src/components/common/ErrorBoundary';
import SplashScreen from './src/screens/Splash/SplashScreen';

export default function App() {
  const { theme } = useAppStore();
  const [isLoading, setIsLoading] = useState(true);

  const handleSplashFinish = () => {
    setIsLoading(false);
  };

  if (isLoading) {
    return (
      <ErrorBoundary>
        <SafeAreaProvider>
          <ThemeProvider>
            <SplashScreen onFinish={handleSplashFinish} />
            <StatusBar style={theme === 'dark' ? 'light' : 'dark'} />
          </ThemeProvider>
        </SafeAreaProvider>
      </ErrorBoundary>
    );
  }

  return (
    <ErrorBoundary>
      <SafeAreaProvider>
        <ThemeProvider>
          <RootNavigator />
          <StatusBar style={theme === 'dark' ? 'light' : 'dark'} />
        </ThemeProvider>
      </SafeAreaProvider>
    </ErrorBoundary>
  );
}
