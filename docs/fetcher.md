# ChainMix Fetcher - 区块链信息聚合接口文档

> 基于 Node.js + Express + TypeScript 的高性能区块链信息聚合系统

## 目录

1. [项目概述](#项目概述)
2. [技术架构](#技术架构)
3. [项目结构](#项目结构)
4. [API 接口](#api-接口)
5. [配置说明](#配置说明)
6. [部署运行](#部署运行)
7. [性能优化](#性能优化)
8. [扩展开发](#扩展开发)

---

## 项目概述

### 核心功能

ChainMix Fetcher 是一个专业的区块链信息聚合系统，为前端移动应用提供统一的 API 接口。主要功能包括：

- 📰 **多源新闻聚合**：整合 ChainCatcher、TechFlow、Foresight News 等多个权威区块链媒体
- ⚡ **实时快讯**：提供最新的区块链行业快讯和重要消息
- 🔍 **智能搜索**：支持跨源搜索和内容过滤
- 🐦 **推特集成**：聚合热门区块链相关推特内容
- 💰 **价格数据**：集成加密货币价格和市场数据
- 🌐 **代理支持**：支持 HTTP/HTTPS/SOCKS5 代理，确保稳定访问
- 🔄 **智能缓存**：多层缓存策略，提升响应速度
- 📊 **统计监控**：完整的请求统计和性能监控

### 技术特色

- ✅ **高性能架构**：异步并发请求，智能负载均衡
- ✅ **代理轮换**：自动代理切换，防止 IP 封禁
- ✅ **请求头管理**：随机 User-Agent、IP 伪装、Referer 设置
- ✅ **容错机制**：自动重试、降级策略、错误隔离
- ✅ **缓存优化**：内存 + 持久化双层缓存
- ✅ **类型安全**：完整的 TypeScript 类型定义和 Zod 验证

---

## 技术架构

### 架构设计

```
┌─────────────────────────────────────────────────────────────┐
│                    Client Applications                      │
│                (Expo + React Native)                       │
└─────────────────────┬───────────────────────────────────────┘
                      │ HTTP/HTTPS
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                  Express Server                            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────────┐│
│  │   Routes    │ │ Controllers │ │    Middleware           ││
│  │             │ │             │ │ - Auth                  ││
│  │ /api/news   │ │   Fetcher   │ │ - Rate Limiting         ││
│  │ /api/feed   │ │  Controller │ │ - Error Handling        ││
│  │ /api/flash  │ │             │ │ - Logging               ││
│  │ /api/search │ │             │ │ - Validation            ││
│  └─────────────┘ └─────────────┘ └─────────────────────────┘│
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                 Fetcher Service                            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────────┐│
│  │   Source    │ │   Proxy     │ │    Cache Manager        ││
│  │  Manager    │ │  Manager    │ │                         ││
│  │             │ │             │ │ - Memory Cache          ││
│  │ - ChainC... │ │ - HTTP      │ │ - Persistent Cache      ││
│  │ - TechFlow  │ │ - HTTPS     │ │ - TTL Management        ││
│  │ - Foresight │ │ - SOCKS5    │ │ - Cache Stats           ││
│  │ - PANews    │ │ - Auth      │ │                         ││
│  │ - TheBlock  │ │ - Rotation  │ │                         ││
│  │ - Odaily    │ │             │ │                         ││
│  └─────────────┘ └─────────────┘ └─────────────────────────┘│
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│              External APIs                                  │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────────┐│
│  │ChainCatcher │ │  TechFlow   │ │    Foresight News       ││
│  │             │ │             │ │                         ││
│  │   PANews    │ │ TheBlockB.. │ │      Odaily             ││
│  └─────────────┘ └─────────────┘ └─────────────────────────┘│
└─────────────────────────────────────────────────────────────┘
```

### 技术栈

| 组件 | 技术 | 版本 | 用途 |
|------|------|------|------|
| **运行环境** | Node.js | >=18.0.0 | JavaScript 运行时 |
| **开发语言** | TypeScript | ^5.3.3 | 静态类型检查 |
| **Web 框架** | Express | ^4.19.2 | HTTP 服务器框架 |
| **HTTP 客户端** | Axios | ^1.6.2 | HTTP 请求库 |
| **数据验证** | Zod | ^3.22.4 | Schema 验证 |
| **缓存管理** | node-cache | ^5.1.2 | 内存缓存 |
| **日志记录** | Winston | ^3.11.0 | 结构化日志 |
| **代理支持** | socks-proxy-agent | ^8.0.5 | SOCKS5 代理 |
| | https-proxy-agent | ^7.0.6 | HTTPS 代理 |
| | http-proxy-agent | ^7.0.2 | HTTP 代理 |
| **安全防护** | Helmet | ^7.1.0 | 安全头设置 |
| **速率限制** | express-rate-limit | ^7.1.5 | API 速率控制 |
| **跨域处理** | CORS | ^2.8.5 | 跨域资源共享 |

---

## 项目结构

### 文件目录

```
server/src/fetcher/
├── index.ts                 # 模块导出入口
├── types.ts                 # TypeScript 类型定义
├── config.ts                # 源配置和代理配置
├── fetcher.ts               # 核心聚合逻辑
├── service.ts               # 业务服务层
├── controller.ts            # 控制器层
└── routes.ts                # 路由定义
```

### 核心模块说明

#### 1. `types.ts` - 类型定义
- 统一响应格式类型
- 外部 API 响应类型
- 配置接口类型
- 验证 Schema 定义

#### 2. `config.ts` - 配置管理
- 信息源配置
- 代理服务器配置
- 环境变量处理
- 动态配置更新

#### 3. `fetcher.ts` - 核心引擎
- 多源数据聚合
- 代理管理和轮换
- 请求头伪装
- 缓存策略实现

#### 4. `service.ts` - 业务逻辑
- 高级业务接口
- 错误处理
- 性能统计
- 配置管理

#### 5. `controller.ts` - 接口控制器
- HTTP 请求处理
- 参数验证
- 响应格式化
- 错误响应

#### 6. `routes.ts` - 路由配置
- API 端点定义
- 中间件配置
- 速率限制
- 文档路由

---

## API 接口

### 基础信息

- **Base URL**: `http://localhost:3000/api`
- **认证方式**: 无需认证（可扩展）
- **响应格式**: JSON
- **字符编码**: UTF-8

### 统一响应格式

```typescript
interface ApiResponse<T> {
  success: boolean;           // 请求是否成功
  data: T;                   // 响应数据
  message?: string;          // 提示信息
  error?: string;            // 错误信息
  timestamp: string;         // 时间戳
}

interface PaginatedResponse<T> {
  items: T[];               // 数据项
  total: number;            // 总数
  page: number;             // 当前页
  limit: number;            // 每页大小
  hasMore: boolean;         // 是否有更多
  totalPages: number;       // 总页数
}
```

### 响应头说明

| 头部 | 说明 | 示例 |
|------|------|------|
| `X-Sources` | 数据来源列表 | `chaincatcher,techflow` |
| `X-Cached` | 是否使用缓存 | `true` |
| `X-Request-Time` | 请求时间戳 | `1702834567890` |
| `X-Source-Errors` | 源错误数量 | `1` |
| `X-Search-Query` | 搜索关键词 | `bitcoin` |

---

### 1. 新闻聚合 API

#### `GET /api/news`

获取聚合新闻数据。

**请求参数**

| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| `page` | number | 否 | 1 | 页码 |
| `limit` | number | 否 | 20 | 每页数量 (1-100) |
| `category` | string | 否 | - | 分类筛选 |
| `sources` | string | 否 | - | 指定源 (逗号分隔) |
| `sortBy` | string | 否 | publishedAt | 排序字段 |
| `sortOrder` | string | 否 | desc | 排序方向 |
| `includeContent` | boolean | 否 | false | 包含正文内容 |

**可用数据源**
- `chaincatcher` - ChainCatcher
- `techflow` - TechFlow深潮
- `foresightnews` - Foresight News
- `panews` - PANews
- `theblockbeats` - TheBlockBeats
- `odaily` - Odaily星球日报

**请求示例**

```bash
# 获取最新新闻
curl "http://localhost:3000/api/news?page=1&limit=10"

# 指定数据源
curl "http://localhost:3000/api/news?sources=chaincatcher,techflow&limit=20"

# 排序和分类
curl "http://localhost:3000/api/news?sortBy=publishedAt&sortOrder=desc&category=defi"
```

**响应示例**

```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": "chaincatcher-2190643",
        "type": "news_flash",
        "title": "数据：ETH 突破 2800 美元",
        "summary": "ChainCatcher 消息，OKX-ETH/USDT 现报 $2798.17，5分钟涨幅0.23%。",
        "content": "ChainCatcher 消息，OKX-ETH/USDT 现报 $2798.17，5分钟涨幅0.23%。",
        "imageUrl": null,
        "publishedAt": "2025-07-10T14:37:00.000Z",
        "tags": ["行情", "ETH"],
        "source": {
          "id": "chaincatcher",
          "name": "ChainCatcher",
          "url": "https://www.chaincatcher.com"
        },
        "keywords": ["行情", "ETH"],
        "newsFlashType": 1,
        "isHot": false
      }
    ],
    "total": 1,
    "page": 1,
    "limit": 10,
    "hasMore": false,
    "totalPages": 1
  },
  "message": "Successfully fetched 1 news items",
  "timestamp": "2025-07-10T17:30:00.000Z"
}
```

---

### 2. Feed 聚合 API

#### `GET /api/feed`

获取聚合 Feed 数据，主要来源于 Foresight News。

**请求参数**

同 `/api/news`

**请求示例**

```bash
curl "http://localhost:3000/api/feed?page=1&limit=30"
```

**响应格式**

同 `/api/news`，但 `type` 为 `"feed"`，包含额外字段：

```json
{
  "type": "feed",
  "isImportant": true,
  "label": "重要消息",
  "wikis": [
    {
      "id": 1999,
      "name": "特斯拉",
      "subscribed": false
    }
  ]
}
```

---

### 3. 快讯 API

#### `GET /api/flash`

获取最新快讯数据。

**请求参数**

同 `/api/news`

**请求示例**

```bash
curl "http://localhost:3000/api/flash?page=1&limit=20"
```

**响应格式**

同 `/api/news`，但数据更新频率更高，缓存时间更短。

---

### 4. 搜索 API

#### `GET /api/search`

跨源搜索新闻内容。

**请求参数**

| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| `q` | string | 是 | - | 搜索关键词 (1-200字符) |
| `page` | number | 否 | 1 | 页码 |
| `limit` | number | 否 | 20 | 每页数量 (1-50) |
| `sources` | string | 否 | - | 指定源 |
| `timeRange` | string | 否 | all | 时间范围 |

**时间范围选项**
- `1h` - 1小时内
- `24h` - 24小时内
- `7d` - 7天内
- `30d` - 30天内
- `all` - 全部

**请求示例**

```bash
# 搜索比特币相关新闻
curl "http://localhost:3000/api/search?q=bitcoin&timeRange=24h"

# 搜索以太坊，限制在特定源
curl "http://localhost:3000/api/search?q=ethereum&sources=chaincatcher,techflow"
```

**响应示例**

```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": "techflow-91128",
        "type": "news_flash",
        "title": "分析师：美国买家推动比特币创历史新高，牛市行情或尚未结束",
        "summary": "分析师表示，Coinbase 溢价指标是衡量美国投资者需求的重要工具...",
        "publishedAt": "2025-07-10T15:37:48.000Z",
        "tags": [],
        "source": {
          "id": "techflow",
          "name": "TechFlow",
          "url": "https://www.techflowpost.com"
        },
        "isHot": true
      }
    ],
    "total": 1,
    "page": 1,
    "limit": 20,
    "hasMore": false,
    "totalPages": 1
  },
  "message": "Found 1 items for \"bitcoin\"",
  "timestamp": "2025-07-10T17:30:00.000Z"
}
```

---

### 5. 热门推特 API

#### `GET /api/hot-tweets`

获取热门区块链相关推特 (占位实现)。

**请求参数**

同 `/api/news`

**请求示例**

```bash
curl "http://localhost:3000/api/hot-tweets?page=1&limit=10"
```

**响应格式**

```json
{
  "success": true,
  "data": {
    "items": [],
    "total": 0,
    "page": 1,
    "limit": 10,
    "hasMore": false,
    "totalPages": 0
  },
  "message": "Successfully fetched 0 hot tweets",
  "timestamp": "2025-07-10T17:30:00.000Z"
}
```

---

### 6. 价格数据 API

#### `GET /api/prices`

获取加密货币价格数据 (占位实现)。

**请求参数**

| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| `symbols` | string | 否 | - | 币种符号 (逗号分隔) |
| `vs_currency` | string | 否 | usd | 计价货币 |
| `limit` | number | 否 | 100 | 数量限制 (1-250) |
| `sources` | string | 否 | - | 指定源 |

**请求示例**

```bash
curl "http://localhost:3000/api/prices?symbols=bitcoin,ethereum&vs_currency=usd"
```

---

### 7. 工具 API

#### `GET /api/sources`

获取可用数据源列表。

**请求示例**

```bash
curl "http://localhost:3000/api/sources"
```

**响应示例**

```json
{
  "success": true,
  "data": [
    {
      "id": "chaincatcher",
      "name": "ChainCatcher",
      "enabled": true
    },
    {
      "id": "techflow",
      "name": "TechFlow",
      "enabled": true
    }
  ],
  "message": "Found 6 available sources",
  "timestamp": "2025-07-10T17:30:00.000Z"
}
```

#### `GET /api/stats`

获取聚合器统计信息。

**请求示例**

```bash
curl "http://localhost:3000/api/stats"
```

**响应示例**

```json
{
  "success": true,
  "data": {
    "totalRequests": 156,
    "successfulRequests": 148,
    "failedRequests": 8,
    "averageResponseTime": 1250,
    "cacheStats": {
      "hits": 89,
      "misses": 67,
      "keys": 45,
      "hitRate": 0.57
    },
    "sourceStats": {
      "chaincatcher": {
        "requests": 45,
        "successes": 42,
        "failures": 3,
        "averageResponseTime": 1100
      }
    }
  },
  "message": "Fetcher statistics retrieved successfully",
  "timestamp": "2025-07-10T17:30:00.000Z"
}
```

#### `POST /api/cache/clear`

清除聚合器缓存。

**请求示例**

```bash
curl -X POST "http://localhost:3000/api/cache/clear"
```

#### `GET /api/health`

健康检查接口。

**响应示例**

```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "uptime": 3600,
    "timestamp": "2025-07-10T17:30:00.000Z",
    "sources": {
      "total": 6,
      "enabled": 6,
      "disabled": 0
    },
    "cache": {
      "hits": 89,
      "misses": 67,
      "keys": 45
    },
    "requests": {
      "total": 156,
      "successful": 148,
      "failed": 8,
      "successRate": "94.87%"
    }
  },
  "message": "Fetcher is healthy",
  "timestamp": "2025-07-10T17:30:00.000Z"
}
```

---

### 速率限制

| 端点类型 | 限制 | 窗口期 |
|----------|------|--------|
| 默认接口 | 100 请求 | 15 分钟 |
| 搜索接口 | 50 请求 | 15 分钟 |
| 价格接口 | 60 请求 | 1 分钟 |
| 缓存操作 | 5 请求 | 15 分钟 |

---

## 配置说明

### 环境变量

创建 `.env` 文件：

```bash
# 服务器配置
PORT=3000
NODE_ENV=production

# 缓存配置
FETCHER_CACHE_TTL=300
FETCHER_CACHE_MAX_SIZE=1000

# 代理配置
HTTP_PROXY=http://proxy.example.com:8080
HTTPS_PROXY=https://proxy.example.com:8080
SOCKS_PROXY=socks5://user:<EMAIL>:1080

# 禁用的数据源 (逗号分隔)
DISABLED_SOURCES=source1,source2

# CORS 配置
CORS_ORIGIN=http://localhost:3000,https://yourapp.com

# 速率限制
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# 信任代理
TRUST_PROXY=true
```

### 代理配置

支持多种代理类型：

```typescript
// HTTP/HTTPS 代理
{
  protocol: 'http',
  host: '127.0.0.1',
  port: 8080,
  enabled: true
}

// SOCKS5 代理 (带认证)
{
  protocol: 'socks5',
  host: '127.0.0.1',
  port: 1080,
  auth: {
    username: 'user',
    password: 'pass'
  },
  enabled: true
}
```

### 数据源配置

每个数据源支持独立配置：

```typescript
{
  id: 'chaincatcher',
  name: 'ChainCatcher',
  baseUrl: 'https://www.chaincatcher.com',
  headers: {
    'Origin': 'https://www.chaincatcher.com',
    'Referer': 'https://www.chaincatcher.com/news'
  },
  endpoints: {
    news: '/pc/content/page',
    flash: '/pc/content/page'
  },
  rateLimit: {
    requests: 60,
    window: 60000
  },
  timeout: 10000,
  retries: 3,
  enabled: true
}
```

---

## 部署运行

### 安装依赖

```bash
cd server
pnpm install
```

### 开发模式

```bash
pnpm dev
```

### 生产构建

```bash
pnpm build
pnpm start
```

### Docker 部署

```dockerfile
FROM node:18-alpine

WORKDIR /app

# 复制包配置文件
COPY package.json pnpm-lock.yaml ./

# 安装 pnpm 和依赖
RUN npm install -g pnpm
RUN pnpm install --frozen-lockfile

# 复制源代码
COPY . .

# 构建应用
RUN pnpm build

# 暴露端口
EXPOSE 3000

# 启动应用
CMD ["pnpm", "start"]
```

### 健康检查

```bash
# 基础健康检查
curl http://localhost:3000/health

# 聚合器健康检查
curl http://localhost:3000/api/health
```

---

## 性能优化

### 缓存策略

#### 多层缓存架构

```
Level 1: 内存缓存 (node-cache)
├── TTL: 5-10 分钟
├── 容量: 1000 个 key
└── 用途: 热点数据快速访问

Level 2: 持久化缓存 (可扩展 Redis)
├── TTL: 30-60 分钟
├── 容量: 无限制
└── 用途: 冷数据和跨实例共享
```

#### 缓存键策略

```typescript
// 新闻缓存键
news-{sources}-{page}-{limit}-{category}

// 搜索缓存键
search-{query}-{sources}-{page}-{limit}-{timeRange}

// Feed 缓存键
feed-{sources}-{page}-{limit}
```

### 请求优化

#### 并发控制

```typescript
// 并发请求多个数据源
const results = await Promise.all(fetchPromises);

// 请求超时控制
const timeout = 15000; // 15秒

// 重试机制
const retries = 3;
```

#### 代理轮换

```typescript
// 自动代理切换
const proxy = this.proxyManager.getNextProxy();

// 失败自动重试
if (error) {
  await new Promise(resolve => 
    setTimeout(resolve, Math.pow(2, attempt) * 1000)
  );
}
```

### 监控指标

#### 性能指标

- **响应时间**: 平均响应时间 < 2000ms
- **成功率**: 请求成功率 > 95%
- **缓存命中率**: 缓存命中率 > 60%
- **并发处理**: 支持 100+ 并发请求

#### 告警机制

```typescript
// 错误率告警
if (errorRate > 0.1) {
  logger.error('High error rate detected', { errorRate });
}

// 响应时间告警
if (averageResponseTime > 5000) {
  logger.warn('High response time detected', { averageResponseTime });
}
```

---

## 扩展开发

### 添加新数据源

#### 1. 定义数据源配置

```typescript
// config.ts
export const SOURCE_CONFIGS: SourceConfig[] = [
  // ... 现有配置
  {
    id: 'new-source',
    name: 'New Blockchain News',
    baseUrl: 'https://api.newblockchain.com',
    headers: {
      'Authorization': 'Bearer YOUR_API_KEY',
      'Content-Type': 'application/json'
    },
    endpoints: {
      news: '/v1/news',
      flash: '/v1/flash'
    },
    enabled: true
  }
];
```

#### 2. 实现数据转换器

```typescript
// fetcher.ts - DataTransformer 类
static transformNewSource(response: any, sourceId: string): UnifiedItem[] {
  if (!response.data?.articles) return [];
  
  return response.data.articles.map((item: any) => ({
    id: `${sourceId}-${item.id}`,
    type: 'news_article' as const,
    title: item.headline,
    summary: item.excerpt,
    content: item.body,
    imageUrl: item.image_url,
    publishedAt: new Date(item.created_at).toISOString(),
    tags: item.categories || [],
    source: {
      id: sourceId,
      name: 'New Blockchain News',
      url: 'https://newblockchain.com',
    },
  }));
}

// 注册转换器
static getTransformer(sourceId: string) {
  switch (sourceId) {
    // ... 现有 case
    case 'new-source':
      return this.transformNewSource;
    default:
      return () => [];
  }
}
```

#### 3. 测试新数据源

```typescript
// 单元测试
describe('NewSource Integration', () => {
  it('should fetch and transform data correctly', async () => {
    const fetcher = new BlockchainFetcher(config);
    const result = await fetcher.fetchNews(['new-source'], 1, 10);
    
    expect(result.items).toBeDefined();
    expect(result.sources).toContain('new-source');
  });
});
```

### 添加新接口类型

#### 1. 定义类型

```typescript
// types.ts
export interface EventItem extends BaseArticle {
  type: 'event';
  startDate: string;
  endDate: string;
  location: string;
  registrationUrl?: string;
}
```

#### 2. 实现获取方法

```typescript
// fetcher.ts
async fetchEvents(
  sources?: string[],
  page: number = 1,
  limit: number = 20,
  options: FetchOptions = {}
): Promise<AggregatedResult<EventItem>> {
  // 实现事件获取逻辑
}
```

#### 3. 添加路由

```typescript
// routes.ts
router.get('/events',
  defaultLimiter,
  validateRequest({ query: FetcherQuerySchema }),
  fetcherController.getEvents.bind(fetcherController)
);
```

### 中间件扩展

#### 认证中间件

```typescript
// middlewares/auth.ts
export const authMiddleware = (req: Request, res: Response, next: NextFunction) => {
  const token = req.headers.authorization;
  
  if (!token) {
    return res.status(401).json({
      success: false,
      error: 'Authentication required'
    });
  }
  
  // 验证 token
  next();
};
```

#### 数据过滤中间件

```typescript
// middlewares/filter.ts
export const contentFilter = (req: Request, res: Response, next: NextFunction) => {
  // 内容过滤逻辑
  next();
};
```

### 缓存扩展

#### Redis 缓存实现

```typescript
// utils/redis-cache.ts
import Redis from 'ioredis';

export class RedisCacheManager {
  private redis: Redis;
  
  constructor() {
    this.redis = new Redis(process.env.REDIS_URL);
  }
  
  async get<T>(key: string): Promise<T | null> {
    const value = await this.redis.get(key);
    return value ? JSON.parse(value) : null;
  }
  
  async set(key: string, value: any, ttl: number): Promise<void> {
    await this.redis.setex(key, ttl, JSON.stringify(value));
  }
  
  async del(key: string): Promise<void> {
    await this.redis.del(key);
  }
}
```

---

## 故障排除

### 常见问题

#### 1. 代理连接失败

**问题**: 所有代理都无法连接

**解决方案**:
```bash
# 检查代理配置
curl --proxy socks5://proxy.example.com:1080 http://httpbin.org/ip

# 测试无代理访问
curl http://www.chaincatcher.com

# 检查防火墙设置
iptables -L | grep -i drop
```

#### 2. 请求频率限制

**问题**: 源站返回 429 错误

**解决方案**:
```typescript
// 调整请求间隔
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));
await delay(1000); // 等待 1 秒

// 降低并发数
const concurrency = 2; // 同时最多 2 个请求
```

#### 3. 内存占用过高

**问题**: 缓存占用过多内存

**解决方案**:
```typescript
// 调整缓存大小
const cache = new NodeCache({
  maxKeys: 500,        // 减少最大 key 数量
  checkperiod: 30,     // 更频繁的清理
});

// 监控内存使用
console.log('Memory usage:', process.memoryUsage());
```

### 日志分析

#### 启用详细日志

```typescript
// logger.ts
export const logger = winston.createLogger({
  level: 'debug', // 启用调试日志
  transports: [
    new winston.transports.File({ 
      filename: 'fetcher-debug.log',
      level: 'debug'
    })
  ]
});
```

#### 日志查询

```bash
# 查看错误日志
tail -f logs/error.log | grep "fetcher"

# 统计请求成功率
grep "✅.*requests" logs/combined.log | wc -l
grep "❌.*requests" logs/combined.log | wc -l
```

---

## 版本更新

### v1.0.0 (2025-07-10)

#### 新功能
- ✅ 完整的多源新闻聚合系统
- ✅ 智能代理管理和轮换
- ✅ 多层缓存架构
- ✅ 完整的 TypeScript 类型支持
- ✅ 统计监控和健康检查
- ✅ RESTful API 设计

#### 支持的数据源
- ✅ ChainCatcher
- ✅ TechFlow深潮
- ✅ Foresight News
- ✅ PANews
- ✅ TheBlockBeats
- ✅ Odaily星球日报

#### 计划功能
- 🔄 Twitter API 集成
- 🔄 价格数据 API 集成
- 🔄 WebSocket 实时推送
- 🔄 Redis 缓存支持
- 🔄 Elasticsearch 搜索
- 🔄 图片代理和优化

---

## 贡献指南

### 开发环境

```bash
# 克隆项目
git clone https://github.com/chainmix/backend.git
cd backend/server

# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev
```

### 代码规范

```bash
# 代码检查
pnpm lint

# 代码格式化
pnpm format

# 运行测试
pnpm test

# 类型检查
pnpm build
```

### 提交规范

```bash
git commit -m "feat: 添加新的数据源支持"
git commit -m "fix: 修复代理连接问题"
git commit -m "docs: 更新 API 文档"
```

---

## 许可证

MIT License - 详见 [LICENSE](../LICENSE) 文件。

---

*文档版本: 1.0.0*  
*最后更新: 2025-07-10*  
*作者: ChainMix Team*