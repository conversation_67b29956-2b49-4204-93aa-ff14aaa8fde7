{"name": "chainmix-backend", "version": "1.0.0", "description": "Node.js + Express + TypeScript backend service for ChainMix blockchain news app", "main": "dist/app.js", "scripts": {"start": "node dist/app.js", "dev": "ts-node-dev --respawn --transpile-only -r tsconfig-paths/register src/app.ts", "build": "tsc", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "clean": "<PERSON><PERSON><PERSON> dist", "prepare": "npm run build"}, "keywords": ["blockchain", "cryptocurrency", "news", "api", "express", "typescript", "nodejs"], "author": "ChainMix Team", "license": "MIT", "dependencies": {"@jest/globals": "^30.0.4", "axios": "^1.6.2", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "http-proxy-agent": "^7.0.2", "https-proxy-agent": "^7.0.6", "node-cache": "^5.1.2", "pako": "^2.1.0", "socks-proxy-agent": "^8.0.5", "tsconfig-paths": "^4.2.0", "winston": "^3.11.0", "zod": "^3.22.4"}, "devDependencies": {"@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/node": "^20.10.5", "@types/pako": "^2.0.3", "@types/supertest": "^6.0.2", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "eslint": "^8.56.0", "jest": "^29.7.0", "prettier": "^3.1.1", "rimraf": "^5.0.5", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}