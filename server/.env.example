# Environment Configuration
NODE_ENV=development
PORT=3000

# CORS Configuration
CORS_ORIGIN=http://localhost:8081,exp://localhost:19000

# API Keys
NEWS_API_KEY=your_newsapi_key_here
COINGECKO_API_KEY=your_coingecko_api_key_here

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Cache Configuration
CACHE_TTL_MINUTES=5
CACHE_CHECK_PERIOD_MINUTES=10

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log

# External API URLs
NEWS_API_BASE_URL=https://newsapi.org/v2
COINGECKO_API_BASE_URL=https://api.coingecko.com/api/v3

# Security
HELMET_CSP_ENABLED=true
TRUST_PROXY=false