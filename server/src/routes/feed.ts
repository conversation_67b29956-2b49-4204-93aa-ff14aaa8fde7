import { Router } from 'express';
import { feedController } from '@/controllers/feed';

const router: Router = Router();

/**
 * @route GET /api/feed
 * @desc Get paginated feed items
 * @access Public
 * @query {number} page - Page number (default: 1)
 * @query {number} limit - Items per page (default: 20, max: 100)
 */
router.get('/', feedController.getFeed);

/**
 * @route GET /api/feed/search
 * @desc Search feed items
 * @access Public
 * @query {string} q - Search query (required)
 * @query {number} page - Page number (default: 1)
 * @query {number} limit - Items per page (default: 20, max: 50)
 */
router.get('/search', feedController.searchFeed);

export default router;