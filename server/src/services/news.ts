import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { logger, logHelpers } from '@/utils/logger';
import { cacheManager, cacheKeys } from '@/utils/cache';
import {
  NewsApiResponse,
  NewsApiArticle,
  NewsArticle,
  NewsCategory,
  ServiceOptions,
  ExternalApiConfig,
} from '@/types';
import { handleExternalApiError } from '@/middlewares/error';

/**
 * NewsAPI.org service for fetching blockchain and cryptocurrency news
 */
class NewsService {
  private readonly apiClient: AxiosInstance;
  private readonly config: ExternalApiConfig;

  // News categories mapping
  private readonly categoryMapping: Record<string, string> = {
    'defi': 'DeFi OR "decentralized finance"',
    'nft': 'NFT OR "non-fungible token"',
    'cryptocurrency': 'cryptocurrency OR bitcoin OR ethereum',
    'blockchain-tech': 'blockchain OR "distributed ledger"',
    'regulation': 'crypto regulation OR "cryptocurrency law"',
    'market-analysis': 'crypto market OR "cryptocurrency price"',
  };

  // Default news categories for ChainMix
  private readonly defaultCategories: NewsCategory[] = [
    {
      id: '1',
      name: 'DeFi',
      slug: 'defi',
      color: '#007AFF',
      icon: 'trending-up',
      description: 'Decentralized Finance news and updates',
    },
    {
      id: '2',
      name: 'NFT',
      slug: 'nft',
      color: '#5856D6',
      icon: 'image',
      description: 'Non-Fungible Token market and developments',
    },
    {
      id: '3',
      name: 'Cryptocurrency',
      slug: 'cryptocurrency',
      color: '#FF9500',
      icon: 'logo-bitcoin',
      description: 'General cryptocurrency news and updates',
    },
    {
      id: '4',
      name: 'Blockchain Technology',
      slug: 'blockchain-tech',
      color: '#34C759',
      icon: 'cube',
      description: 'Blockchain technology and innovations',
    },
    {
      id: '5',
      name: 'Regulation',
      slug: 'regulation',
      color: '#FF3B30',
      icon: 'shield',
      description: 'Cryptocurrency regulations and policies',
    },
    {
      id: '6',
      name: 'Market Analysis',
      slug: 'market-analysis',
      color: '#AF52DE',
      icon: 'analytics',
      description: 'Market trends and price analysis',
    },
  ];

  constructor() {
    this.config = {
      baseUrl: process.env.NEWS_API_BASE_URL || 'https://newsapi.org/v2',
      apiKey: process.env.NEWS_API_KEY,
      timeout: 10000,
      retries: 3,
      rateLimit: {
        requests: 1000,
        window: 24 * 60 * 60 * 1000, // 24 hours
      },
    };

    this.apiClient = axios.create({
      baseURL: this.config.baseUrl,
      timeout: this.config.timeout,
      headers: {
        'Authorization': `Bearer ${this.config.apiKey}`,
        'User-Agent': 'ChainMix-Backend/1.0.0',
      },
    });

    this.setupInterceptors();
  }

  /**
   * Setup axios interceptors for logging and error handling
   */
  private setupInterceptors(): void {
    // Request interceptor
    this.apiClient.interceptors.request.use(
      (config) => {
        logger.debug('NewsAPI request', {
          method: config.method,
          url: config.url,
          params: config.params,
        });
        return config;
      },
      (error) => {
        logger.error('NewsAPI request error', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.apiClient.interceptors.response.use(
      (response: AxiosResponse) => {
        logHelpers.logExternalApiCall(
          'NewsAPI',
          response.config.url || '',
          response.config.method?.toUpperCase() || 'GET',
          response.status,
          Date.now() - (response.config.metadata?.startTime || Date.now())
        );
        return response;
      },
      (error) => {
        const statusCode = error.response?.status || 0;
        logHelpers.logExternalApiCall(
          'NewsAPI',
          error.config?.url || '',
          error.config?.method?.toUpperCase() || 'GET',
          statusCode,
          Date.now() - (error.config?.metadata?.startTime || Date.now())
        );
        return Promise.reject(error);
      }
    );
  }

  /**
   * Get news articles with pagination and filtering
   */
  public async getArticles(
    page: number = 1,
    limit: number = 20,
    category?: string,
    options: ServiceOptions = {}
  ): Promise<{ articles: NewsArticle[]; total: number; hasMore: boolean }> {
    try {
      const cacheKey = cacheKeys.news.articles(page, limit, category);
      
      // Check cache first
      const cachedResult = cacheManager.get<{ articles: NewsArticle[]; total: number; hasMore: boolean }>(cacheKey);
      if (cachedResult && !options.cacheKey) {
        logHelpers.logExternalApiCall('NewsAPI', '/everything', 'GET', 200, 0, true);
        return cachedResult;
      }

      // Build search query
      const searchQuery = this.buildSearchQuery(category);
      
      const response = await this.apiClient.get<NewsApiResponse>('/everything', {
        params: {
          q: searchQuery,
          language: 'en',
          sortBy: 'publishedAt',
          page,
          pageSize: Math.min(limit, 100), // NewsAPI max page size is 100
          domains: 'coindesk.com,cointelegraph.com,decrypt.co,theblock.co,bitcoin.com',
        },
        metadata: { startTime: Date.now() },
      });

      const { articles: rawArticles, totalResults } = response.data;
      
      // Transform articles to our format
      const articles = rawArticles
        .filter(this.filterValidArticles)
        .map(article => this.transformArticle(article, category));

      const result = {
        articles,
        total: totalResults,
        hasMore: page * limit < totalResults,
      };

      // Cache the result
      cacheManager.set(cacheKey, result, options.cacheTTL || 300); // 5 minutes default

      return result;
    } catch (error) {
      throw handleExternalApiError(error, 'NewsAPI');
    }
  }

  /**
   * Search articles by query
   */
  public async searchArticles(
    query: string,
    page: number = 1,
    limit: number = 20,
    options: ServiceOptions = {}
  ): Promise<{ articles: NewsArticle[]; total: number; hasMore: boolean }> {
    try {
      const cacheKey = cacheKeys.news.search(query, page, limit);
      
      // Check cache first
      const cachedResult = cacheManager.get<{ articles: NewsArticle[]; total: number; hasMore: boolean }>(cacheKey);
      if (cachedResult && !options.cacheKey) {
        logHelpers.logExternalApiCall('NewsAPI', '/everything', 'GET', 200, 0, true);
        return cachedResult;
      }

      // Enhance search query with crypto context
      const enhancedQuery = `(${query}) AND (cryptocurrency OR blockchain OR bitcoin OR ethereum OR crypto)`;

      const response = await this.apiClient.get<NewsApiResponse>('/everything', {
        params: {
          q: enhancedQuery,
          language: 'en',
          sortBy: 'relevancy',
          page,
          pageSize: Math.min(limit, 100),
        },
        metadata: { startTime: Date.now() },
      });

      const { articles: rawArticles, totalResults } = response.data;
      
      const articles = rawArticles
        .filter(this.filterValidArticles)
        .map(article => this.transformArticle(article));

      const result = {
        articles,
        total: totalResults,
        hasMore: page * limit < totalResults,
      };

      // Cache the result
      cacheManager.set(cacheKey, result, options.cacheTTL || 180); // 3 minutes for search

      return result;
    } catch (error) {
      throw handleExternalApiError(error, 'NewsAPI');
    }
  }

  /**
   * Get trending articles
   */
  public async getTrendingArticles(
    limit: number = 10,
    options: ServiceOptions = {}
  ): Promise<NewsArticle[]> {
    try {
      const cacheKey = cacheKeys.news.trending(limit);
      
      const cachedResult = cacheManager.get<NewsArticle[]>(cacheKey);
      if (cachedResult && !options.cacheKey) {
        logHelpers.logExternalApiCall('NewsAPI', '/top-headlines', 'GET', 200, 0, true);
        return cachedResult;
      }

      const response = await this.apiClient.get<NewsApiResponse>('/top-headlines', {
        params: {
          q: 'cryptocurrency OR bitcoin OR blockchain',
          language: 'en',
          pageSize: limit,
          sortBy: 'popularity',
        },
        metadata: { startTime: Date.now() },
      });

      const articles = response.data.articles
        .filter(this.filterValidArticles)
        .map(article => this.transformArticle(article));

      // Cache the result
      cacheManager.set(cacheKey, articles, options.cacheTTL || 600); // 10 minutes

      return articles;
    } catch (error) {
      throw handleExternalApiError(error, 'NewsAPI');
    }
  }

  /**
   * Get article by ID (simulated since NewsAPI doesn't support this)
   */
  public async getArticleById(id: string): Promise<NewsArticle | null> {
    // Since NewsAPI doesn't support getting by ID, we'll try to find it in cache
    // or simulate based on the URL structure
    const cacheKey = cacheKeys.news.article(id);
    const cachedArticle = cacheManager.get<NewsArticle>(cacheKey);
    
    if (cachedArticle) {
      return cachedArticle;
    }

    // If not in cache, return null or implement fallback logic
    return null;
  }

  /**
   * Get news categories
   */
  public async getCategories(): Promise<NewsCategory[]> {
    const cacheKey = cacheKeys.news.categories();
    
    const cachedCategories = cacheManager.get<NewsCategory[]>(cacheKey);
    if (cachedCategories) {
      return cachedCategories;
    }

    // Cache the default categories
    cacheManager.set(cacheKey, this.defaultCategories, 3600); // 1 hour

    return this.defaultCategories;
  }

  /**
   * Build search query based on category
   */
  private buildSearchQuery(category?: string): string {
    if (category && this.categoryMapping[category]) {
      return `(${this.categoryMapping[category]}) AND (cryptocurrency OR blockchain OR crypto)`;
    }
    
    return 'cryptocurrency OR blockchain OR bitcoin OR ethereum OR "digital currency"';
  }

  /**
   * Filter valid articles
   */
  private filterValidArticles = (article: NewsApiArticle): boolean => {
    return !!(
      article.title &&
      article.description &&
      article.publishedAt &&
      article.url &&
      !article.title.includes('[Removed]') &&
      !article.description.includes('[Removed]')
    );
  };

  /**
   * Transform NewsAPI article to our format
   */
  private transformArticle = (article: NewsApiArticle, categorySlug?: string): NewsArticle => {
    // Determine category based on content or use provided category
    const category = this.determineCategory(article, categorySlug);
    
    // Calculate read time (rough estimate: 200 words per minute)
    const wordCount = (article.content || article.description || '').split(' ').length;
    const readTime = Math.max(1, Math.ceil(wordCount / 200));

    // Extract hashtags from content
    const tags = this.extractTags(article.title + ' ' + (article.description || ''));

    return {
      id: this.generateArticleId(article.url),
      title: article.title,
      summary: article.description || '',
      content: article.content || article.description || '',
      imageUrl: article.urlToImage || undefined,
      category,
      publishedAt: article.publishedAt,
      author: article.author || article.source.name,
      readTime,
      tags,
      source: {
        name: article.source.name,
        url: article.url,
      },
      engagement: {
        views: Math.floor(Math.random() * 10000) + 100, // Simulated
        likes: Math.floor(Math.random() * 500) + 10,    // Simulated
        shares: Math.floor(Math.random() * 100) + 5,    // Simulated
      },
    };
  };

  /**
   * Determine article category based on content
   */
  private determineCategory(article: NewsApiArticle, categorySlug?: string): NewsCategory {
    if (categorySlug) {
      const category = this.defaultCategories.find(cat => cat.slug === categorySlug);
      if (category) return category;
    }

    const content = (article.title + ' ' + (article.description || '')).toLowerCase();
    
    if (content.includes('defi') || content.includes('decentralized finance')) {
      return this.defaultCategories[0]; // DeFi
    }
    if (content.includes('nft') || content.includes('non-fungible')) {
      return this.defaultCategories[1]; // NFT
    }
    if (content.includes('regulation') || content.includes('law') || content.includes('legal')) {
      return this.defaultCategories[4]; // Regulation
    }
    if (content.includes('price') || content.includes('market') || content.includes('trading')) {
      return this.defaultCategories[5]; // Market Analysis
    }
    if (content.includes('blockchain') || content.includes('technology')) {
      return this.defaultCategories[3]; // Blockchain Tech
    }
    
    // Default to Cryptocurrency
    return this.defaultCategories[2];
  }

  /**
   * Extract tags from article content
   */
  private extractTags(content: string): string[] {
    const commonCryptoTerms = [
      'bitcoin', 'ethereum', 'crypto', 'blockchain', 'defi', 'nft',
      'cryptocurrency', 'trading', 'mining', 'wallet', 'exchange',
      'altcoin', 'stablecoin', 'smart contract', 'web3', 'metaverse'
    ];

    const contentLower = content.toLowerCase();
    const foundTags = commonCryptoTerms.filter(term => 
      contentLower.includes(term)
    );

    return foundTags.slice(0, 5); // Limit to 5 tags
  }

  /**
   * Generate consistent article ID from URL
   */
  private generateArticleId(url: string): string {
    // Create a simple hash of the URL for consistent IDs
    let hash = 0;
    for (let i = 0; i < url.length; i++) {
      const char = url.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }
}

// Export singleton instance
export const newsService = new NewsService();
export default newsService;