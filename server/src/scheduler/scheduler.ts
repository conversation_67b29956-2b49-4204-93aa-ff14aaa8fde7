import { fetcherService } from '../fetcher/service';
import { UnifiedItem } from '../fetcher/types';
import { logger } from '@/utils/logger';
import { performance } from 'perf_hooks';
import crypto from 'crypto';
import NodeCache from 'node-cache';

export interface SchedulerConfig {
  interval: number; // 间隔时间（毫秒）
  maxRetries: number; // 最大重试次数
  batchSize: number; // 每批获取的数据量
  deduplication: {
    enabled: boolean;
    titleSimilarityThreshold: number; // 标题相似度阈值
    contentSimilarityThreshold: number; // 内容相似度阈值
    timeWindowHours: number; // 时间窗口（小时）
  };
}

export interface CollectedData {
  items: UnifiedItem[];
  lastUpdated: Date;
  totalItems: number;
  sources: string[];
  errors: Array<{ source: string; error: string; timestamp: Date }>;
}

export class DataScheduler {
  private static instance: DataScheduler;
  private config: SchedulerConfig;
  private cache: NodeCache;
  private isRunning: boolean = false;
  private intervalId: NodeJS.Timeout | null = null;
  private collectedData: CollectedData;

  private constructor(config: SchedulerConfig) {
    this.config = config;
    this.cache = new NodeCache({
      stdTTL: 60 * 60 * 24, // 24小时缓存
      maxKeys: 10000,
      checkperiod: 60 * 10, // 每10分钟检查过期
    });
    
    this.collectedData = {
      items: [],
      lastUpdated: new Date(),
      totalItems: 0,
      sources: [],
      errors: [],
    };
  }

  public static getInstance(config?: SchedulerConfig): DataScheduler {
    if (!DataScheduler.instance) {
      const defaultConfig: SchedulerConfig = {
        interval: 5 * 60 * 1000, // 5分钟
        maxRetries: 3,
        batchSize: 10,
        deduplication: {
          enabled: true,
          titleSimilarityThreshold: 0.85,
          contentSimilarityThreshold: 0.8,
          timeWindowHours: 24,
        },
      };
      DataScheduler.instance = new DataScheduler(config || defaultConfig);
    }
    return DataScheduler.instance;
  }

  /**
   * 开始定时数据收集
   */
  public start(): void {
    if (this.isRunning) {
      logger.warn('⏰ DataScheduler is already running');
      return;
    }

    this.isRunning = true;
    logger.warn('⏰ DataScheduler is running');
    // 立即执行一次数据收集
    this.collectData();
    
    // 设置定时器
    this.intervalId = setInterval(() => {
      this.collectData();
    }, this.config.interval);

    logger.info('⏰ DataScheduler started', {
      interval: this.config.interval,
      batchSize: this.config.batchSize,
      deduplication: this.config.deduplication.enabled,
    });
  }

  /**
   * 停止定时数据收集
   */
  public stop(): void {
    if (!this.isRunning) {
      logger.warn('⏰ DataScheduler is not running');
      return;
    }

    this.isRunning = false;
    
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }

    logger.info('⏰ DataScheduler stopped');
  }

  /**
   * 收集数据的主要方法
   */
  private async collectData(): Promise<void> {
    const startTime = performance.now();
    
    try {
      logger.info('🔄 Starting data collection cycle');
      
      // 获取所有类型的数据
      const [newsData, feedData, flashData] = await Promise.allSettled([
        fetcherService.getNews({ 
          page: 1, 
          limit: this.config.batchSize,
          sortBy: 'publishedAt',
          sortOrder: 'desc',
          includeContent: false
        }),
        fetcherService.getFeed({ 
          page: 1, 
          limit: this.config.batchSize,
          sortBy: 'publishedAt',
          sortOrder: 'desc',
          includeContent: false
        }),
        fetcherService.getFlash({ 
          page: 1, 
          limit: this.config.batchSize,
          sortBy: 'publishedAt',
          sortOrder: 'desc',
          includeContent: false
        }),
      ]);

      // 合并所有数据
      const allItems: UnifiedItem[] = [];
      const allSources = new Set<string>();
      const errors: Array<{ source: string; error: string; timestamp: Date }> = [];

      // 处理新闻数据
      if (newsData.status === 'fulfilled' && newsData.value.items) {
        allItems.push(...newsData.value.items);
        newsData.value.sources.forEach(source => allSources.add(source));
        if (newsData.value.errors) {
          errors.push(...newsData.value.errors.map(e => ({ ...e, timestamp: new Date() })));
        }
      } else if (newsData.status === 'rejected') {
        errors.push({ source: 'news', error: newsData.reason?.message || 'Unknown error', timestamp: new Date() });
      }

      // 处理订阅源数据
      if (feedData.status === 'fulfilled' && feedData.value.items) {
        allItems.push(...feedData.value.items);
        feedData.value.sources.forEach(source => allSources.add(source));
        if (feedData.value.errors) {
          errors.push(...feedData.value.errors.map(e => ({ ...e, timestamp: new Date() })));
        }
      } else if (feedData.status === 'rejected') {
        errors.push({ source: 'feed', error: feedData.reason?.message || 'Unknown error', timestamp: new Date() });
      }

      // 处理快讯数据
      if (flashData.status === 'fulfilled' && flashData.value.items) {
        allItems.push(...flashData.value.items);
        flashData.value.sources.forEach(source => allSources.add(source));
        if (flashData.value.errors) {
          errors.push(...flashData.value.errors.map(e => ({ ...e, timestamp: new Date() })));
        }
      } else if (flashData.status === 'rejected') {
        errors.push({ source: 'flash', error: flashData.reason?.message || 'Unknown error', timestamp: new Date() });
      }

      // 去重和聚合数据
      const deduplicatedItems = this.config.deduplication.enabled 
        ? await this.deduplicateItems(allItems)
        : allItems;

      // 按时间排序
      deduplicatedItems.sort((a, b) => 
        new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime()
      );

      // 更新收集的数据
      this.collectedData = {
        items: deduplicatedItems,
        lastUpdated: new Date(),
        totalItems: deduplicatedItems.length,
        sources: Array.from(allSources),
        errors,
      };

      // 缓存数据
      this.cache.set('collected_data', this.collectedData);

      const endTime = performance.now();
      logger.info('✅ Data collection completed', {
        totalItems: deduplicatedItems.length,
        sources: Array.from(allSources),
        errors: errors.length,
        duration: Math.round(endTime - startTime),
        cached: true,
      });

    } catch (error) {
      logger.error('❌ Data collection failed:', error);
      
      // 记录错误
      this.collectedData.errors.push({
        source: 'scheduler',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date(),
      });
    }
  }

  /**
   * 数据去重逻辑
   */
  private async deduplicateItems(items: UnifiedItem[]): Promise<UnifiedItem[]> {
    const startTime = performance.now();
    const deduplicatedItems: UnifiedItem[] = [];
    const seenHashes = new Set<string>();
    
    // 按时间排序，保留最新的
    items.sort((a, b) => new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime());

    for (const item of items) {
      // 生成内容哈希
      const contentHash = this.generateContentHash(item);
      
      if (seenHashes.has(contentHash)) {
        continue; // 跳过重复内容
      }

      // 检查是否与已有项目相似
      const isDuplicate = this.config.deduplication.enabled && 
        deduplicatedItems.some(existingItem => 
          this.isSimilarItem(item, existingItem)
        );

      if (!isDuplicate) {
        deduplicatedItems.push(item);
        seenHashes.add(contentHash);
      }
    }

    const endTime = performance.now();
    logger.info('🔄 Deduplication completed', {
      originalCount: items.length,
      deduplicatedCount: deduplicatedItems.length,
      removedCount: items.length - deduplicatedItems.length,
      duration: Math.round(endTime - startTime),
    });

    return deduplicatedItems;
  }

  /**
   * 生成内容哈希
   */
  private generateContentHash(item: UnifiedItem): string {
    let content = '';
    if (item.type === 'twitter') {
      content = `${item.content}${item.author.username}${item.source.id}`;
    } else {
      content = `${item.title}${item.summary}${item.source.id}`;
    }
    return crypto.createHash('md5').update(content).digest('hex');
  }

  /**
   * 判断两个项目是否相似
   */
  private isSimilarItem(item1: UnifiedItem, item2: UnifiedItem): boolean {
    // 时间窗口检查
    const timeDiff = Math.abs(
      new Date(item1.publishedAt).getTime() - new Date(item2.publishedAt).getTime()
    );
    const timeWindowMs = this.config.deduplication.timeWindowHours * 60 * 60 * 1000;
    
    if (timeDiff > timeWindowMs) {
      return false;
    }

    // 处理不同类型的项目
    if (item1.type !== item2.type) {
      return false;
    }

    if (item1.type === 'twitter' && item2.type === 'twitter') {
      // Twitter 项目比较
      const contentSimilarity = this.calculateSimilarity(item1.content, item2.content);
      return contentSimilarity >= this.config.deduplication.contentSimilarityThreshold;
    } else if (item1.type !== 'twitter' && item2.type !== 'twitter') {
      // 其他类型项目比较
      const titleSimilarity = this.calculateSimilarity(item1.title, item2.title);
      if (titleSimilarity >= this.config.deduplication.titleSimilarityThreshold) {
        return true;
      }

      const contentSimilarity = this.calculateSimilarity(item1.summary, item2.summary);
      if (contentSimilarity >= this.config.deduplication.contentSimilarityThreshold) {
        return true;
      }
    }

    return false;
  }

  /**
   * 计算字符串相似度（简化版Jaccard相似度）
   */
  private calculateSimilarity(str1: string, str2: string): number {
    if (!str1 || !str2) return 0;
    
    const words1 = new Set(str1.toLowerCase().split(/\s+/));
    const words2 = new Set(str2.toLowerCase().split(/\s+/));
    
    const intersection = new Set([...words1].filter(word => words2.has(word)));
    const union = new Set([...words1, ...words2]);
    
    return intersection.size / union.size;
  }

  /**
   * 获取收集的数据
   */
  public getCollectedData(): CollectedData {
    return this.collectedData;
  }

  /**
   * 获取分页数据
   */
  public getPagedData(page: number = 1, limit: number = 20, type?: string): {
    items: UnifiedItem[];
    total: number;
    page: number;
    limit: number;
    hasMore: boolean;
  } {
    let items = this.collectedData.items;
    
    // 按类型过滤
    if (type) {
      items = items.filter(item => item.type === type);
    }

    const total = items.length;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const pagedItems = items.slice(startIndex, endIndex);

    return {
      items: pagedItems,
      total,
      page,
      limit,
      hasMore: endIndex < total,
    };
  }

  /**
   * 搜索数据
   */
  public searchData(query: string, page: number = 1, limit: number = 20): {
    items: UnifiedItem[];
    total: number;
    page: number;
    limit: number;
    hasMore: boolean;
  } {
    const searchTerm = query.toLowerCase();
    const filteredItems = this.collectedData.items.filter(item => {
      if (item.type === 'twitter') {
        return item.content.toLowerCase().includes(searchTerm) ||
               item.hashtags.some((tag: string) => tag.toLowerCase().includes(searchTerm));
      } else {
        return item.title.toLowerCase().includes(searchTerm) ||
               item.summary.toLowerCase().includes(searchTerm) ||
               item.tags.some((tag: string) => tag.toLowerCase().includes(searchTerm));
      }
    });

    const total = filteredItems.length;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const pagedItems = filteredItems.slice(startIndex, endIndex);

    return {
      items: pagedItems,
      total,
      page,
      limit,
      hasMore: endIndex < total,
    };
  }

  /**
   * 获取统计信息
   */
  public getStats(): {
    totalItems: number;
    sources: string[];
    lastUpdated: Date;
    errors: number;
    isRunning: boolean;
    config: SchedulerConfig;
  } {
    return {
      totalItems: this.collectedData.totalItems,
      sources: this.collectedData.sources,
      lastUpdated: this.collectedData.lastUpdated,
      errors: this.collectedData.errors.length,
      isRunning: this.isRunning,
      config: this.config,
    };
  }

  /**
   * 更新配置
   */
  public updateConfig(newConfig: Partial<SchedulerConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    // 如果间隔时间变化，重启定时器
    if (newConfig.interval && this.isRunning) {
      this.stop();
      this.start();
    }
    
    logger.info('⚙️ Scheduler configuration updated', this.config);
  }

  /**
   * 清除缓存
   */
  public clearCache(): void {
    this.cache.flushAll();
    logger.info('🗑️ Scheduler cache cleared');
  }
}

// 导出单例实例
export const dataScheduler = DataScheduler.getInstance();