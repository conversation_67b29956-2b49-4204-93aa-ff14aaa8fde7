import express, { Application } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import dotenv from 'dotenv';
// Load environment variables
dotenv.config();

// Import middleware
import { errorHandler } from '@/middlewares/error';
import { requestLogger } from '@/middlewares/logger';

// Import routes
import newsRoutes from '@/routes/news';
import feedRoutes from '@/routes/feed';
import flashRoutes from '@/routes/flash';
import pricesRoutes from '@/routes/prices';
import categoriesRoutes from '@/routes/categories';
import searchRoutes from '@/routes/search';
import { fetcherRoutes } from '@/fetcher';

// Import utilities
import { logger } from '@/utils/logger';
import { cacheManager } from '@/utils/cache';
import { dataScheduler } from '@/scheduler/scheduler';

/**
 * Express application setup
 */
class App {
  public app: Application;
  public port: number;

  constructor() {
    this.app = express();
    this.port = parseInt(process.env.PORT || '3000', 10);

    this.initializeMiddleware();
    this.initializeRoutes();
    this.initializeErrorHandling();
  }

  /**
   * Initialize Express middleware
   */
  private initializeMiddleware(): void {
    // Security middleware
    this.app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"],
        },
      },
      crossOriginEmbedderPolicy: false,
    }));

    // CORS configuration
    const corsOptions = {
      origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'],
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
      credentials: true,
      optionsSuccessStatus: 200,
    };
    this.app.use(cors(corsOptions));

    // Compression
    this.app.use(compression());

    // Body parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Rate limiting
    const limiter = rateLimit({
      windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000', 10), // 15 minutes
      max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100', 10), // limit each IP to 100 requests per windowMs
      message: {
        error: 'Too many requests from this IP, please try again later.',
        retryAfter: Math.ceil(parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000', 10) / 1000),
      },
      standardHeaders: true,
      legacyHeaders: false,
    });
    this.app.use('/api', limiter);

    // Request logging
    this.app.use(requestLogger);

    // Trust proxy if behind reverse proxy
    if (process.env.TRUST_PROXY === 'true') {
      this.app.set('trust proxy', 1);
    }
  }

  /**
   * Initialize API routes
   */
  private initializeRoutes(): void {
    // Health check endpoint
    this.app.get('/health', (_, res) => {
      res.status(200).json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: process.env.NODE_ENV,
        version: process.env.npm_package_version || '1.0.0',
      });
    });

    // API routes
    this.app.use('/api/news', newsRoutes);
    this.app.use('/api/feed', feedRoutes);
    this.app.use('/api/flash', flashRoutes);
    this.app.use('/api/prices', pricesRoutes);
    this.app.use('/api/categories', categoriesRoutes);
    this.app.use('/api/search', searchRoutes);
    this.app.use('/api', fetcherRoutes); // Aggregated fetcher routes

    // API documentation endpoint
    this.app.get('/api', (_, res) => {
      res.json({
        name: 'ChainMix Backend API',
        version: '1.0.0',
        description: 'Backend service for ChainMix blockchain news mobile application',
        endpoints: {
          health: 'GET /health',
          news: 'GET /api/news',
          feed: 'GET /api/feed',
          flash: 'GET /api/flash',
          search: 'GET /api/search',
          'hot-tweets': 'GET /api/hot-tweets',
          prices: 'GET /api/prices',
          categories: 'GET /api/categories',
          sources: 'GET /api/sources',
          stats: 'GET /api/stats',
        },
        documentation: 'https://github.com/chainmix/backend#readme',
      });
    });

    // 404 handler
    this.app.use('*', (req, res) => {
      res.status(404).json({
        success: false,
        error: 'Route not found',
        message: `The requested route ${req.originalUrl} does not exist`,
        availableRoutes: [
          '/health',
          '/api',
          '/api/news',
          '/api/feed',
          '/api/flash',
          '/api/prices',
          '/api/categories',
          '/api/search',
        ],
      });
    });
  }

  /**
   * Initialize error handling
   */
  private initializeErrorHandling(): void {
    this.app.use(errorHandler);
  }

  /**
   * Start the Express server
   */
  public listen(): void {
    this.app.listen(this.port, () => {
      logger.info(`🚀 ChainMix Backend Server started on port ${this.port}`);
      logger.info(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
      logger.info(`🔗 Health check: http://localhost:${this.port}/health`);
      logger.info(`📚 API documentation: http://localhost:${this.port}/api`);
      
      // Initialize cache cleanup
      cacheManager.startCleanupTimer();
      
      // Start data scheduler automatically
      logger.info('⏰ Starting data scheduler...');
      dataScheduler.start();
    });
  }

  /**
   * Graceful shutdown handler
   */
  public setupGracefulShutdown(): void {
    const shutdown = (signal: string): void => {
      logger.info(`🛑 Received ${signal}, starting graceful shutdown...`);
      
      // Stop data scheduler
      dataScheduler.stop();
      
      // Stop accepting new connections
      process.exit(0);
    };

    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGINT', () => shutdown('SIGINT'));

    // Handle uncaught exceptions
    process.on('uncaughtException', (error: Error) => {
      logger.error('Uncaught Exception:', error);
      dataScheduler.stop();
      process.exit(1);
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason: unknown, promise: Promise<unknown>) => {
      logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
      dataScheduler.stop();
      process.exit(1);
    });
  }
}

// Create and start the application
const app = new App();
app.setupGracefulShutdown();

// Start server only if not in test environment
if (process.env.NODE_ENV !== 'test') {
  app.listen();
}

export default app.app;