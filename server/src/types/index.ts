import { z } from 'zod';

// ========================================
// Base Types
// ========================================

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
  timestamp: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
  totalPages: number;
}

export interface ApiError {
  code: string;
  message: string;
  details?: Record<string, unknown>;
  stack?: string;
}

// Re-export fetcher types for consistency
export * from '@/fetcher/types';

// ========================================
// News Types
// ========================================

export interface NewsCategory {
  id: string;
  name: string;
  slug: string;
  color: string;
  icon: string;
  description?: string;
}

export interface NewsArticle {
  id: string;
  title: string;
  summary: string;
  content: string;
  imageUrl?: string;
  category: NewsCategory;
  publishedAt: string;
  author: string;
  readTime: number; // in minutes
  tags: string[];
  source: {
    name: string;
    url: string;
  };
  engagement?: {
    views: number;
    likes: number;
    shares: number;
  };
}

export interface TwitterPost {
  id: string;
  author: {
    username: string;
    displayName: string;
    avatar: string;
    verified: boolean;
  };
  content: string;
  publishedAt: string;
  engagement: {
    likes: number;
    retweets: number;
    replies: number;
  };
  hashtags: string[];
  urls: string[];
}

// ========================================
// Cryptocurrency Types
// ========================================

export interface CryptoCurrency {
  id: string;
  symbol: string;
  name: string;
  image: string;
  currentPrice: number;
  marketCap: number;
  marketCapRank: number;
  fullyDilutedValuation?: number | null;
  totalVolume: number;
  high24h: number;
  low24h: number;
  priceChange24h: number;
  priceChangePercentage24h: number;
  marketCapChange24h: number;
  marketCapChangePercentage24h: number;
  circulatingSupply: number;
  totalSupply?: number | null;
  maxSupply?: number | null;
  ath: number;
  athChangePercentage: number;
  athDate: string;
  atl: number;
  atlChangePercentage: number;
  atlDate: string;
  lastUpdated: string;
}

export interface PriceHistory {
  timestamp: number;
  price: number;
}

export interface CryptoPriceData {
  currency: CryptoCurrency;
  priceHistory: PriceHistory[];
  timeframe: '1h' | '24h' | '7d' | '30d' | '1y';
}

// ========================================
// External API Types
// ========================================

// NewsAPI.org response types
export interface NewsApiArticle {
  source: {
    id: string | null;
    name: string;
  };
  author: string | null;
  title: string;
  description: string | null;
  url: string;
  urlToImage: string | null;
  publishedAt: string;
  content: string | null;
}

export interface NewsApiResponse {
  status: string;
  totalResults: number;
  articles: NewsApiArticle[];
}

// CoinGecko API response types
export interface CoinGeckoPrice {
  id: string;
  symbol: string;
  name: string;
  image: string;
  current_price: number;
  market_cap: number;
  market_cap_rank: number;
  fully_diluted_valuation: number | null;
  total_volume: number;
  high_24h: number;
  low_24h: number;
  price_change_24h: number;
  price_change_percentage_24h: number;
  market_cap_change_24h: number;
  market_cap_change_percentage_24h: number;
  circulating_supply: number;
  total_supply: number | null;
  max_supply: number | null;
  ath: number;
  ath_change_percentage: number;
  ath_date: string;
  atl: number;
  atl_change_percentage: number;
  atl_date: string;
  last_updated: string;
}

// ========================================
// Request/Response Validation Schemas
// ========================================

// News API schemas
export const GetNewsQuerySchema = z.object({
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(20),
  category: z.string().optional(),
  sortBy: z.enum(['publishedAt', 'relevance', 'popularity']).default('publishedAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

export const SearchNewsQuerySchema = z.object({
  q: z.string().min(1).max(100),
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(50).default(20),
  category: z.string().optional(),
  sortBy: z.enum(['publishedAt', 'relevance']).default('relevance'),
});

// Prices API schemas
export const GetPricesQuerySchema = z.object({
  ids: z.string().optional(),
  vs_currency: z.string().default('usd'),
  order: z.enum(['market_cap_desc', 'market_cap_asc', 'volume_desc', 'volume_asc']).default('market_cap_desc'),
  per_page: z.coerce.number().min(1).max(250).default(100),
  page: z.coerce.number().min(1).default(1),
  sparkline: z.coerce.boolean().default(false),
});

export const GetPriceHistoryQuerySchema = z.object({
  id: z.string(),
  vs_currency: z.string().default('usd'),
  days: z.enum(['1', '7', '14', '30', '90', '180', '365', 'max']).default('7'),
  interval: z.enum(['minutely', 'hourly', 'daily']).optional(),
});

// Categories API schemas
export const GetCategoriesQuerySchema = z.object({
  type: z.enum(['news', 'crypto']).default('news'),
});

// ========================================
// Service Types
// ========================================

export interface CacheItem<T = unknown> {
  value: T;
  expiresAt: number;
}

export interface ServiceOptions {
  timeout?: number;
  retries?: number;
  cacheKey?: string;
  cacheTTL?: number;
}

export interface ExternalApiConfig {
  baseUrl: string;
  apiKey?: string;
  timeout: number;
  retries: number;
  rateLimit?: {
    requests: number;
    window: number; // in ms
  };
}

// ========================================
// Express Types
// ========================================

export interface RequestWithUser extends Express.Request {
  user?: {
    id: string;
    email: string;
    role: string;
  };
}

export interface CustomError extends Error {
  statusCode?: number;
  code?: string;
  details?: Record<string, unknown>;
}

// ========================================
// Type Guards
// ========================================

export const isNewsApiArticle = (obj: unknown): obj is NewsApiArticle => {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'title' in obj &&
    'publishedAt' in obj &&
    'source' in obj
  );
};

export const isCoinGeckoPrice = (obj: unknown): obj is CoinGeckoPrice => {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'id' in obj &&
    'symbol' in obj &&
    'current_price' in obj
  );
};

// ========================================
// Utility Types
// ========================================

export type GetNewsQuery = z.infer<typeof GetNewsQuerySchema>;
export type SearchNewsQuery = z.infer<typeof SearchNewsQuerySchema>;
export type GetPricesQuery = z.infer<typeof GetPricesQuerySchema>;
export type GetPriceHistoryQuery = z.infer<typeof GetPriceHistoryQuerySchema>;
export type GetCategoriesQuery = z.infer<typeof GetCategoriesQuerySchema>;

// Response wrapper types
export type NewsResponse = ApiResponse<PaginatedResponse<NewsArticle>>;
export type ArticleResponse = ApiResponse<NewsArticle>;
export type CategoriesResponse = ApiResponse<NewsCategory[]>;
export type PricesResponse = ApiResponse<CryptoCurrency[]>;
export type PriceHistoryResponse = ApiResponse<CryptoPriceData>;
export type TwitterResponse = ApiResponse<PaginatedResponse<TwitterPost>>;

// Error response type
export type ErrorResponse = ApiResponse<null> & {
  success: false;
  error: string;
  details?: Record<string, unknown>;
};