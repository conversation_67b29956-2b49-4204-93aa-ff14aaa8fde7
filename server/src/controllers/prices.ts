import { Request, Response } from 'express';
import { pricesService } from '@/services/prices';
import { logger } from '@/utils/logger';
import {
  GetPricesQuery,
  GetPriceHistoryQuery,
  PricesResponse,
  PriceHistoryResponse,
  ApiResponse,
  CryptoCurrency,
} from '@/types';
import { asyncHandler } from '@/middlewares/error';

/**
 * Prices Controller
 * Handles all cryptocurrency price-related API endpoints
 */
class PricesController {
  /**
   * Get cryptocurrency prices with pagination
   * GET /api/prices
   */
  public getPrices = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const query = req.query as unknown as GetPricesQuery;
    const { vs_currency, order, per_page, page, sparkline } = query;

    logger.info('Fetching cryptocurrency prices', {
      currency: vs_currency,
      order,
      perPage: per_page,
      page,
      sparkline,
      requestId: res.getHeader('x-request-id'),
    });

    const startTime = Date.now();

    // Get cryptocurrencies from prices service
    const { currencies, hasMore } = await pricesService.getCryptocurrencies(
      page,
      per_page,
      vs_currency,
      order
    );

    const duration = Date.now() - startTime;

    logger.info('Cryptocurrency prices fetched successfully', {
      currenciesCount: currencies.length,
      currency: vs_currency,
      hasMore,
      duration: `${duration}ms`,
      requestId: res.getHeader('x-request-id'),
    });

    const response: PricesResponse = {
      success: true,
      data: currencies,
      message: 'Cryptocurrency prices retrieved successfully',
      timestamp: new Date().toISOString(),
    };

    res.status(200).json(response);
  });

  /**
   * Get specific cryptocurrency price data
   * GET /api/prices/:id
   */
  public getCryptocurrencyById = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { id } = req.params;
    const currency = (req.query.vs_currency as string) || 'usd';

    if (!id) {
      res.status(400).json({
        success: false,
        data: null,
        error: 'Cryptocurrency ID is required',
        timestamp: new Date().toISOString(),
      });
      return;
    }

    logger.info('Fetching cryptocurrency by ID', {
      cryptoId: id,
      currency,
      requestId: res.getHeader('x-request-id'),
    });

    const startTime = Date.now();

    // Get cryptocurrency from prices service
    const cryptocurrency = await pricesService.getCryptocurrency(id, currency);

    const duration = Date.now() - startTime;

    logger.info('Cryptocurrency fetched successfully', {
      cryptoId: id,
      cryptoName: cryptocurrency.name,
      currentPrice: cryptocurrency.currentPrice,
      duration: `${duration}ms`,
      requestId: res.getHeader('x-request-id'),
    });

    const response: ApiResponse<CryptoCurrency> = {
      success: true,
      data: cryptocurrency,
      message: 'Cryptocurrency data retrieved successfully',
      timestamp: new Date().toISOString(),
    };

    res.status(200).json(response);
  });

  /**
   * Get price history for a cryptocurrency
   * GET /api/prices/:id/history
   */
  public getPriceHistory = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { id } = req.params;
    const query = req.query as unknown as GetPriceHistoryQuery;
    const { vs_currency, days, interval } = query;

    if (!id) {
      res.status(400).json({
        success: false,
        data: null,
        error: 'Cryptocurrency ID is required',
        timestamp: new Date().toISOString(),
      });
      return;
    }

    logger.info('Fetching price history', {
      cryptoId: id,
      currency: vs_currency,
      days,
      interval,
      requestId: res.getHeader('x-request-id'),
    });

    const startTime = Date.now();

    // Get price history from prices service
    const priceData = await pricesService.getPriceHistory(id, days, vs_currency);

    const duration = Date.now() - startTime;

    logger.info('Price history fetched successfully', {
      cryptoId: id,
      cryptoName: priceData.currency.name,
      dataPoints: priceData.priceHistory.length,
      timeframe: priceData.timeframe,
      duration: `${duration}ms`,
      requestId: res.getHeader('x-request-id'),
    });

    const response: PriceHistoryResponse = {
      success: true,
      data: priceData,
      message: `Price history for ${priceData.currency.name} retrieved successfully`,
      timestamp: new Date().toISOString(),
    };

    res.status(200).json(response);
  });

  /**
   * Get trending cryptocurrencies
   * GET /api/prices/trending
   */
  public getTrendingCryptocurrencies = asyncHandler(async (_: Request, res: Response): Promise<void> => {
    logger.info('Fetching trending cryptocurrencies', {
      requestId: res.getHeader('x-request-id'),
    });

    const startTime = Date.now();

    // Get trending cryptocurrencies from prices service
    const trendingCryptos = await pricesService.getTrendingCryptocurrencies();

    const duration = Date.now() - startTime;

    logger.info('Trending cryptocurrencies fetched successfully', {
      count: trendingCryptos.length,
      duration: `${duration}ms`,
      requestId: res.getHeader('x-request-id'),
    });

    const response: ApiResponse<CryptoCurrency[]> = {
      success: true,
      data: trendingCryptos,
      message: 'Trending cryptocurrencies retrieved successfully',
      timestamp: new Date().toISOString(),
    };

    res.status(200).json(response);
  });

  /**
   * Get top cryptocurrencies by market cap
   * GET /api/prices/top
   */
  public getTopCryptocurrencies = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const limit = parseInt(req.query.limit as string) || 10;
    const currency = (req.query.vs_currency as string) || 'usd';

    logger.info('Fetching top cryptocurrencies', {
      limit,
      currency,
      requestId: res.getHeader('x-request-id'),
    });

    const startTime = Date.now();

    // Get top cryptocurrencies from prices service
    const topCryptos = await pricesService.getTopCryptocurrencies(limit, currency);

    const duration = Date.now() - startTime;

    logger.info('Top cryptocurrencies fetched successfully', {
      count: topCryptos.length,
      currency,
      duration: `${duration}ms`,
      requestId: res.getHeader('x-request-id'),
    });

    const response: ApiResponse<CryptoCurrency[]> = {
      success: true,
      data: topCryptos,
      message: 'Top cryptocurrencies retrieved successfully',
      timestamp: new Date().toISOString(),
    };

    res.status(200).json(response);
  });

  /**
   * Get market overview statistics
   * GET /api/prices/market-overview
   */
  public getMarketOverview = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const currency = (req.query.vs_currency as string) || 'usd';

    logger.info('Fetching market overview', {
      currency,
      requestId: res.getHeader('x-request-id'),
    });

    const startTime = Date.now();

    // Get top cryptocurrencies to calculate market overview
    const topCryptos = await pricesService.getTopCryptocurrencies(100, currency);

    // Calculate market statistics
    const totalMarketCap = topCryptos.reduce((sum, crypto) => sum + crypto.marketCap, 0);
    const totalVolume24h = topCryptos.reduce((sum, crypto) => sum + crypto.totalVolume, 0);
    const averageChange24h = topCryptos.reduce((sum, crypto) => 
      sum + crypto.priceChangePercentage24h, 0) / topCryptos.length;

    // Count gainers and losers
    const gainers = topCryptos.filter(crypto => crypto.priceChangePercentage24h > 0).length;
    const losers = topCryptos.filter(crypto => crypto.priceChangePercentage24h < 0).length;

    const marketOverview = {
      totalMarketCap,
      totalVolume24h,
      averageChange24h,
      topCryptocurrencies: topCryptos.slice(0, 10), // Top 10
      marketSentiment: {
        gainers,
        losers,
        neutral: topCryptos.length - gainers - losers,
      },
      dominance: {
        bitcoin: topCryptos.find(c => c.id === 'bitcoin')?.marketCap || 0,
        ethereum: topCryptos.find(c => c.id === 'ethereum')?.marketCap || 0,
      },
    };

    const duration = Date.now() - startTime;

    logger.info('Market overview fetched successfully', {
      totalMarketCap,
      cryptosAnalyzed: topCryptos.length,
      duration: `${duration}ms`,
      requestId: res.getHeader('x-request-id'),
    });

    const response: ApiResponse<typeof marketOverview> = {
      success: true,
      data: marketOverview,
      message: 'Market overview retrieved successfully',
      timestamp: new Date().toISOString(),
    };

    res.status(200).json(response);
  });
}

// Export singleton instance
export const pricesController = new PricesController();
export default pricesController;