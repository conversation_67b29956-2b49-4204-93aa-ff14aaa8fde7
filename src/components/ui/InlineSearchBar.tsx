import React, { useState, useRef, useEffect } from "react";
import {
  View,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Dimensions,
  FlatList,
  Text,
  Keyboard,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useTheme } from "../common/ThemeProvider";
import {
  SPACING,
  FONT_SIZES,
  BORDER_RADIUS,
  FONT_WEIGHTS,
  SPRING_CONFIG,
} from "../../constants";
import TouchableScale from "./TouchableScale";

const { width: screenWidth } = Dimensions.get("window");

interface SearchSuggestion {
  id: string;
  text: string;
  type: "history" | "suggestion";
}

interface InlineSearchBarProps {
  onSearch: (query: string) => void;
  suggestions?: SearchSuggestion[];
  searchHistory?: string[];
  onClearHistory?: () => void;
  style?: any;
}

const InlineSearchBar: React.FC<InlineSearchBarProps> = ({
  onSearch,
  suggestions = [],
  searchHistory = [],
  onClearHistory,
  style,
}) => {
  const { colors } = useTheme();
  const [isExpanded, setIsExpanded] = useState(false);
  const [query, setQuery] = useState("");
  const [showSuggestions, setShowSuggestions] = useState(false);

  const inputRef = useRef<TextInput>(null);
  const expandAnim = useRef(new Animated.Value(0)).current;
  const suggestionAnim = useRef(new Animated.Value(0)).current;

  const styles = createStyles(colors);

  // 计算搜索框展开的宽度（屏幕宽度 - 左侧标题空间 - 边距）
  const expandedWidth = screenWidth - 120 - SPACING.md * 2;
  const collapsedWidth = 44; // 圆形按钮的大小

  useEffect(() => {
    if (isExpanded) {
      Animated.spring(expandAnim, {
        toValue: 1,
        useNativeDriver: false,
        tension: SPRING_CONFIG.tension,
        friction: SPRING_CONFIG.friction,
      }).start();
    } else {
      Animated.spring(expandAnim, {
        toValue: 0,
        useNativeDriver: false,
        tension: SPRING_CONFIG.tension,
        friction: SPRING_CONFIG.friction,
      }).start();
    }
  }, [isExpanded]);

  useEffect(() => {
    if (showSuggestions && (query.length > 0 || searchHistory.length > 0)) {
      Animated.spring(suggestionAnim, {
        toValue: 1,
        useNativeDriver: true,
        tension: SPRING_CONFIG.tension,
        friction: SPRING_CONFIG.friction,
      }).start();
    } else {
      Animated.spring(suggestionAnim, {
        toValue: 0,
        useNativeDriver: true,
        tension: SPRING_CONFIG.tension,
        friction: SPRING_CONFIG.friction,
      }).start();
    }
  }, [showSuggestions, query, searchHistory.length]);

  const handleSearchPress = () => {
    if (!isExpanded) {
      setIsExpanded(true);
      setTimeout(() => {
        inputRef.current?.focus();
      }, 200);
    }
  };

  const handleInputFocus = () => {
    setShowSuggestions(true);
  };

  const handleInputBlur = () => {
    // 延迟关闭以允许点击建议
    setTimeout(() => {
      setShowSuggestions(false);
    }, 150);
  };

  const handleSearch = (searchText?: string) => {
    const searchQuery = searchText || query;
    if (searchQuery.trim()) {
      onSearch(searchQuery.trim());
      setQuery(searchQuery);
      setIsExpanded(false);
      setShowSuggestions(false);
      Keyboard.dismiss();
    }
  };

  const handleCancel = () => {
    setQuery("");
    setIsExpanded(false);
    setShowSuggestions(false);
    Keyboard.dismiss();
  };

  const handleSuggestionPress = (suggestion: SearchSuggestion) => {
    handleSearch(suggestion.text);
  };

  const generateSuggestions = (): SearchSuggestion[] => {
    if (!query.trim()) {
      return searchHistory.slice(0, 5).map((item, index) => ({
        id: `history-${index}`,
        text: item,
        type: "history",
      }));
    }

    const filtered = suggestions.filter((item) =>
      item.text.toLowerCase().includes(query.toLowerCase())
    );

    const historySuggestions = searchHistory
      .filter((item) => item.toLowerCase().includes(query.toLowerCase()))
      .slice(0, 3)
      .map((item, index) => ({
        id: `history-${index}`,
        text: item,
        type: "history" as const,
      }));

    return [...historySuggestions, ...filtered].slice(0, 8);
  };

  const currentSuggestions = generateSuggestions();

  const searchBarWidth = expandAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [collapsedWidth, expandedWidth],
  });

  const inputOpacity = expandAnim.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: [0, 0, 1],
  });

  const cancelOpacity = expandAnim.interpolate({
    inputRange: [0, 0.7, 1],
    outputRange: [0, 0, 1],
  });

  const renderSuggestionItem = ({ item }: { item: SearchSuggestion }) => (
    <TouchableScale
      style={styles.suggestionItem}
      onPress={() => handleSuggestionPress(item)}
    >
      <Ionicons
        name={item.type === "history" ? "time-outline" : "search-outline"}
        size={16}
        color={colors.textSecondary}
        style={styles.suggestionIcon}
      />
      <Text style={styles.suggestionText} numberOfLines={1}>
        {item.text}
      </Text>
    </TouchableScale>
  );

  return (
    <View style={[styles.container, style]}>
      <Animated.View
        style={[
          isExpanded ? styles.searchContainer : styles.collapsedContainer,
          {
            width: searchBarWidth,
          },
        ]}
      >
        <TouchableOpacity
          style={[
            styles.searchButton,
            !isExpanded && styles.circularSearchButton,
          ]}
          onPress={handleSearchPress}
          activeOpacity={0.7}
        >
          <Ionicons
            name="search"
            size={20}
            color={isExpanded ? colors.primary : colors.text}
          />
        </TouchableOpacity>

        {isExpanded ? (
          <Animated.View
            style={[
              styles.inputContainer,
              {
                opacity: inputOpacity,
              },
            ]}
          >
            <TextInput
              ref={inputRef}
              style={styles.input}
              placeholder="搜索信息..."
              placeholderTextColor={colors.textSecondary}
              value={query}
              onChangeText={setQuery}
              onFocus={handleInputFocus}
              onBlur={handleInputBlur}
              onSubmitEditing={() => handleSearch()}
              returnKeyType="search"
              selectionColor={colors.primary}
            />
          </Animated.View>
        ) : null}

        {isExpanded ? (
          <Animated.View
            style={[
              styles.cancelContainer,
              {
                opacity: cancelOpacity,
              },
            ]}
          >
            <TouchableScale onPress={handleCancel} style={styles.cancelButton}>
              <Text style={styles.cancelText}>取消</Text>
            </TouchableScale>
          </Animated.View>
        ) : null}
      </Animated.View>

      {showSuggestions && isExpanded && (
        <Animated.View
          style={[
            styles.suggestionsContainer,
            {
              opacity: suggestionAnim,
              transform: [
                {
                  translateY: suggestionAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [-10, 0],
                  }),
                },
              ],
            },
          ]}
        >
          <FlatList
            data={currentSuggestions}
            renderItem={renderSuggestionItem}
            keyExtractor={(item) => item.id}
            style={styles.suggestionsList}
            keyboardShouldPersistTaps="handled"
            showsVerticalScrollIndicator={false}
            ListHeaderComponent={
              query.length === 0 && searchHistory.length > 0 ? (
                <View style={styles.suggestionHeader}>
                  <Text style={styles.suggestionHeaderText}>最近搜索</Text>
                  {onClearHistory && (
                    <TouchableOpacity onPress={onClearHistory}>
                      <Text style={styles.clearHistoryText}>清空</Text>
                    </TouchableOpacity>
                  )}
                </View>
              ) : null
            }
          />
        </Animated.View>
      )}
    </View>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      position: "relative",
      zIndex: 1000,
    },
    searchContainer: {
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: colors.surface,
      borderRadius: BORDER_RADIUS.full,
      paddingHorizontal: SPACING.sm,
      paddingVertical: SPACING.sm,
      shadowColor: colors.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    searchButton: {
      padding: SPACING.xs,
    },
    circularSearchButton: {
      width: 44,
      height: 44,
      borderRadius: 22,
      backgroundColor: colors.surface,
      justifyContent: "center",
      alignItems: "center",
      shadowColor: colors.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
      padding: 0,
    },
    collapsedContainer: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
    },
    inputContainer: {
      flex: 1,
      marginHorizontal: SPACING.sm,
    },
    input: {
      fontSize: FONT_SIZES.base,
      color: colors.text,
      fontWeight: FONT_WEIGHTS.medium,
      paddingVertical: 0,
    },
    cancelContainer: {
      marginLeft: SPACING.sm,
    },
    cancelButton: {
      paddingHorizontal: SPACING.sm,
      paddingVertical: SPACING.xs,
    },
    cancelText: {
      fontSize: FONT_SIZES.sm,
      color: colors.primary,
      fontWeight: FONT_WEIGHTS.medium,
    },
    suggestionsContainer: {
      position: "absolute",
      top: 50,
      left: 0,
      right: 0,
      backgroundColor: colors.surfaceElevated,
      borderRadius: BORDER_RADIUS.lg,
      shadowColor: colors.shadow,
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.15,
      shadowRadius: 12,
      elevation: 8,
      maxHeight: 300,
    },
    suggestionsList: {
      maxHeight: 280,
    },
    suggestionHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingHorizontal: SPACING.md,
      paddingVertical: SPACING.sm,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    suggestionHeaderText: {
      fontSize: FONT_SIZES.sm,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textSecondary,
    },
    clearHistoryText: {
      fontSize: FONT_SIZES.sm,
      color: colors.primary,
      fontWeight: FONT_WEIGHTS.medium,
    },
    suggestionItem: {
      flexDirection: "row",
      alignItems: "center",
      paddingHorizontal: SPACING.md,
      paddingVertical: SPACING.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.borderLight,
    },
    suggestionIcon: {
      marginRight: SPACING.sm,
    },
    suggestionText: {
      flex: 1,
      fontSize: FONT_SIZES.base,
      color: colors.text,
      fontWeight: FONT_WEIGHTS.medium,
    },
  });

export default InlineSearchBar;
