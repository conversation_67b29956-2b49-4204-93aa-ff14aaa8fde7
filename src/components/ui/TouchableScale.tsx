import React, { useRef } from 'react';
import {
  TouchableOpacity,
  Animated,
  ViewStyle,
  TouchableOpacityProps,
} from 'react-native';
import { SPRING_CONFIG } from '../../constants';

interface TouchableScaleProps extends TouchableOpacityProps {
  children: React.ReactNode;
  scaleValue?: number;
  style?: ViewStyle | ViewStyle[];
  disabled?: boolean;
}

const TouchableScale: React.FC<TouchableScaleProps> = ({
  children,
  scaleValue = 0.95,
  style,
  disabled = false,
  onPressIn,
  onPressOut,
  ...props
}) => {
  const scaleAnim = useRef(new Animated.Value(1)).current;

  const handlePressIn = (event: any) => {
    if (!disabled) {
      Animated.spring(scaleAnim, {
        toValue: scaleValue,
        useNativeDriver: true,
        tension: SPRING_CONFIG.tension,
        friction: SPRING_CONFIG.friction,
      }).start();
    }
    onPressIn?.(event);
  };

  const handlePressOut = (event: any) => {
    if (!disabled) {
      Animated.spring(scaleAnim, {
        toValue: 1,
        useNativeDriver: true,
        tension: SPRING_CONFIG.tension,
        friction: SPRING_CONFIG.friction,
      }).start();
    }
    onPressOut?.(event);
  };

  return (
    <TouchableOpacity
      {...props}
      disabled={disabled}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      activeOpacity={1}
    >
      <Animated.View
        style={[
          style,
          {
            transform: [{ scale: scaleAnim }],
          },
        ]}
      >
        {children}
      </Animated.View>
    </TouchableOpacity>
  );
};

export default TouchableScale;
