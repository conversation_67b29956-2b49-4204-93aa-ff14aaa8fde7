import { NewsArticle, NewsCategory, ApiResponse, PaginatedResponse } from '../types';
import { MOCK_ARTICLES } from './mockData';
import { NEWS_CATEGORIES } from '../constants';
import { cacheManager } from '../utils/cache';

// 模拟API延迟
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// 模拟API错误
const shouldSimulateError = () => Math.random() < 0.1; // 10% 概率出错

class ApiService {
  private baseUrl = 'https://api.blockchainnews.com';

  // 获取文章列表
  async getArticles(
    page: number = 1,
    limit: number = 20,
    category?: string
  ): Promise<ApiResponse<PaginatedResponse<NewsArticle>>> {
    const cacheKey = `articles_${page}_${limit}_${category || 'all'}`;

    // 尝试从缓存获取
    const cachedData = await cacheManager.get<ApiResponse<PaginatedResponse<NewsArticle>>>(cacheKey);
    if (cachedData) {
      return cachedData;
    }

    await delay(800); // 模拟网络延迟

    if (shouldSimulateError()) {
      throw new Error('网络连接失败，请稍后重试');
    }

    try {
      let filteredArticles = MOCK_ARTICLES;

      // 按分类筛选
      if (category) {
        filteredArticles = MOCK_ARTICLES.filter(
          article => article.category.slug === category
        );
      }

      // 分页处理
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      const paginatedArticles = filteredArticles.slice(startIndex, endIndex);

      const response: PaginatedResponse<NewsArticle> = {
        items: paginatedArticles,
        total: filteredArticles.length,
        page,
        limit,
        hasMore: endIndex < filteredArticles.length,
      };

      const result = {
        success: true,
        data: response,
        message: '获取文章列表成功',
      };

      // 缓存结果
      await cacheManager.set(cacheKey, result, 5 * 60 * 1000); // 5分钟缓存

      return result;
    } catch (error) {
      throw new Error('获取文章列表失败');
    }
  }

  // 获取单篇文章详情
  async getArticleById(id: string): Promise<ApiResponse<NewsArticle>> {
    await delay(500);

    if (shouldSimulateError()) {
      throw new Error('网络连接失败，请稍后重试');
    }

    const article = MOCK_ARTICLES.find(article => article.id === id);

    if (!article) {
      throw new Error('文章不存在');
    }

    return {
      success: true,
      data: article,
      message: '获取文章详情成功',
    };
  }

  // 获取分类列表
  async getCategories(): Promise<ApiResponse<NewsCategory[]>> {
    await delay(300);

    if (shouldSimulateError()) {
      throw new Error('网络连接失败，请稍后重试');
    }

    return {
      success: true,
      data: NEWS_CATEGORIES,
      message: '获取分类列表成功',
    };
  }

  // 搜索文章
  async searchArticles(
    query: string,
    page: number = 1,
    limit: number = 20
  ): Promise<ApiResponse<PaginatedResponse<NewsArticle>>> {
    await delay(600);

    if (shouldSimulateError()) {
      throw new Error('搜索失败，请稍后重试');
    }

    const searchResults = MOCK_ARTICLES.filter(article =>
      article.title.toLowerCase().includes(query.toLowerCase()) ||
      article.summary.toLowerCase().includes(query.toLowerCase()) ||
      article.content.toLowerCase().includes(query.toLowerCase()) ||
      article.tags.some(tag => tag.toLowerCase().includes(query.toLowerCase()))
    );

    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedResults = searchResults.slice(startIndex, endIndex);

    const response: PaginatedResponse<NewsArticle> = {
      items: paginatedResults,
      total: searchResults.length,
      page,
      limit,
      hasMore: endIndex < searchResults.length,
    };

    return {
      success: true,
      data: response,
      message: '搜索完成',
    };
  }

  // 获取热门文章
  async getTrendingArticles(limit: number = 10): Promise<ApiResponse<NewsArticle[]>> {
    await delay(400);

    if (shouldSimulateError()) {
      throw new Error('获取热门文章失败');
    }

    // 模拟热门文章（取前几篇）
    const trendingArticles = MOCK_ARTICLES.slice(0, limit);

    return {
      success: true,
      data: trendingArticles,
      message: '获取热门文章成功',
    };
  }

  // 获取推荐文章
  async getRecommendedArticles(
    articleId: string,
    limit: number = 5
  ): Promise<ApiResponse<NewsArticle[]>> {
    await delay(500);

    if (shouldSimulateError()) {
      throw new Error('获取推荐文章失败');
    }

    const currentArticle = MOCK_ARTICLES.find(article => article.id === articleId);
    
    if (!currentArticle) {
      throw new Error('文章不存在');
    }

    // 模拟推荐逻辑：同分类的其他文章
    const recommendedArticles = MOCK_ARTICLES
      .filter(article => 
        article.id !== articleId && 
        article.category.id === currentArticle.category.id
      )
      .slice(0, limit);

    return {
      success: true,
      data: recommendedArticles,
      message: '获取推荐文章成功',
    };
  }
}

// 创建API服务实例
export const apiService = new ApiService();

// 导出常用方法
export const {
  getArticles,
  getArticleById,
  getCategories,
  searchArticles,
  getTrendingArticles,
  getRecommendedArticles,
} = apiService;
