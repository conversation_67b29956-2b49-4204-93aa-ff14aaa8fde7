import axios, { AxiosInstance, AxiosError } from 'axios';
import { NewsArticle, NewsCategory, ApiResponse, PaginatedResponse } from '../types';
import { MOCK_ARTICLES } from './mockData';
import { NEWS_CATEGORIES } from '../constants';
import { cacheManager } from '../utils/cache';
import { getCurrentApiConfig, API_ENDPOINTS, CACHE_CONFIG, REQUEST_CONFIG } from '../config/api';

class ApiService {
  private client: AxiosInstance;
  private baseUrl: string;

  constructor() {
    const config = getCurrentApiConfig();
    this.baseUrl = config.baseURL;

    // 创建axios实例
    this.client = axios.create({
      baseURL: this.baseUrl,
      timeout: config.timeout,
      headers: REQUEST_CONFIG.headers,
    });

    // 设置请求拦截器
    this.client.interceptors.request.use(
      (config) => {
        console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('❌ API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // 设置响应拦截器
    this.client.interceptors.response.use(
      (response) => {
        console.log(`✅ API Response: ${response.status} ${response.config.url}`);
        return response;
      },
      (error: AxiosError) => {
        console.error('❌ API Response Error:', error.message);

        // 处理网络错误
        if (!error.response) {
          throw new Error('网络连接失败，请检查网络设置');
        }

        // 处理HTTP错误状态码
        const status = error.response.status;
        switch (status) {
          case 400:
            throw new Error('请求参数错误');
          case 401:
            throw new Error('未授权访问');
          case 403:
            throw new Error('访问被拒绝');
          case 404:
            throw new Error('请求的资源不存在');
          case 500:
            throw new Error('服务器内部错误');
          case 503:
            throw new Error('服务暂时不可用');
          default:
            throw new Error(`请求失败 (${status})`);
        }
      }
    );
  }

  // 数据转换方法：将后端数据转换为前端期望的格式
  private transformNewsArticle = (item: any): NewsArticle => {
    return {
      id: item.id,
      title: item.title,
      summary: item.summary,
      content: item.content,
      imageUrl: item.imageUrl,
      publishedAt: item.publishedAt,
      author: item.author,
      readTime: item.readTime || 3,
      tags: item.tags || [],
      category: this.transformCategory(item.category),
      // 前端特有字段，默认为false
      isFavorite: false,
    };
  };

  // 转换分类数据
  private transformCategory = (category: any): NewsCategory => {
    if (typeof category === 'string') {
      // 如果后端返回的是字符串，查找对应的分类对象
      const foundCategory = NEWS_CATEGORIES.find(cat => cat.slug === category);
      return foundCategory || NEWS_CATEGORIES[0]; // 默认返回第一个分类
    }

    // 如果后端返回的是对象，直接使用
    return {
      id: category.id || category.slug || 'general',
      name: category.name || '综合',
      slug: category.slug || 'general',
      color: category.color || '#007AFF',
      icon: category.icon || 'newspaper',
    };
  };

  // 模拟数据回退方法
  private async getMockArticles(
    page: number = 1,
    limit: number = 20,
    category?: string
  ): Promise<ApiResponse<PaginatedResponse<NewsArticle>>> {
    let filteredArticles = MOCK_ARTICLES;

    // 按分类筛选
    if (category && category !== 'all') {
      filteredArticles = MOCK_ARTICLES.filter(
        article => article.category.slug === category
      );
    }

    // 分页处理
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedArticles = filteredArticles.slice(startIndex, endIndex);

    const response: PaginatedResponse<NewsArticle> = {
      items: paginatedArticles,
      total: filteredArticles.length,
      page,
      limit,
      hasMore: endIndex < filteredArticles.length,
    };

    return {
      success: true,
      data: response,
      message: '使用模拟数据（后端服务不可用）',
    };
  }

  // 模拟搜索方法
  private async getMockSearchResults(
    query: string,
    page: number = 1,
    limit: number = 20
  ): Promise<ApiResponse<PaginatedResponse<NewsArticle>>> {
    const searchResults = MOCK_ARTICLES.filter(article =>
      article.title.toLowerCase().includes(query.toLowerCase()) ||
      article.summary.toLowerCase().includes(query.toLowerCase()) ||
      article.content.toLowerCase().includes(query.toLowerCase()) ||
      article.tags.some(tag => tag.toLowerCase().includes(query.toLowerCase()))
    );

    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedResults = searchResults.slice(startIndex, endIndex);

    const response: PaginatedResponse<NewsArticle> = {
      items: paginatedResults,
      total: searchResults.length,
      page,
      limit,
      hasMore: endIndex < searchResults.length,
    };

    return {
      success: true,
      data: response,
      message: '使用模拟搜索（后端服务不可用）',
    };
  }

  // 获取文章列表
  async getArticles(
    page: number = 1,
    limit: number = 20,
    category?: string
  ): Promise<ApiResponse<PaginatedResponse<NewsArticle>>> {
    try {
      // 构建查询参数
      const queryParams = new URLSearchParams();
      queryParams.append('page', page.toString());
      queryParams.append('limit', limit.toString());
      if (category && category !== 'all') {
        queryParams.append('category', category);
      }

      // 检查缓存
      const cacheKey = `articles_${queryParams.toString()}`;
      const cachedData = await cacheManager.get<ApiResponse<PaginatedResponse<NewsArticle>>>(cacheKey);

      if (cachedData) {
        console.log('📦 Using cached articles data');
        return cachedData;
      }

      // 发送API请求
      const response = await this.client.get(`${API_ENDPOINTS.NEWS}?${queryParams.toString()}`);

      // 转换数据格式以确保兼容性
      const transformedData: PaginatedResponse<NewsArticle> = {
        items: response.data.data.items.map(this.transformNewsArticle),
        total: response.data.data.total,
        page: response.data.data.page,
        limit: response.data.data.limit,
        hasMore: response.data.data.hasMore,
      };

      const result = {
        success: true,
        data: transformedData,
        message: response.data.message || '获取文章列表成功',
      };

      // 缓存结果
      await cacheManager.set(cacheKey, result, CACHE_CONFIG.ARTICLES);

      return result;
    } catch (error) {
      console.error('获取文章列表失败:', error);

      // 尝试从缓存获取
      const cacheKey = `articles_${page}_${limit}_${category || 'all'}`;
      const cachedData = await cacheManager.get<ApiResponse<PaginatedResponse<NewsArticle>>>(cacheKey);

      if (cachedData) {
        console.log('📦 Using cached articles data (fallback)');
        return cachedData;
      }

      // 如果是开发环境且后端未启动，返回模拟数据
      if (__DEV__ && error instanceof Error && error.message.includes('网络连接失败')) {
        console.log('🔄 Backend not available, using mock data');
        return this.getMockArticles(page, limit, category);
      }

      throw new Error(error instanceof Error ? error.message : '获取文章列表失败');
    }
  }

  // 获取单篇文章详情
  async getArticleById(id: string): Promise<ApiResponse<NewsArticle>> {
    try {
      // 检查缓存
      const cacheKey = `article_${id}`;
      const cachedData = await cacheManager.get<ApiResponse<NewsArticle>>(cacheKey);

      if (cachedData) {
        console.log('📦 Using cached article data');
        return cachedData;
      }

      // 发送API请求
      const response = await this.client.get(API_ENDPOINTS.NEWS_DETAIL(id));

      // 转换数据格式
      const transformedArticle = this.transformNewsArticle(response.data.data);

      const result = {
        success: true,
        data: transformedArticle,
        message: response.data.message || '获取文章详情成功',
      };

      // 缓存结果
      await cacheManager.set(cacheKey, result, CACHE_CONFIG.ARTICLE_DETAIL);

      return result;
    } catch (error) {
      console.error('获取文章详情失败:', error);

      // 尝试从缓存获取
      const cacheKey = `article_${id}`;
      const cachedData = await cacheManager.get<ApiResponse<NewsArticle>>(cacheKey);

      if (cachedData) {
        console.log('📦 Using cached article data (fallback)');
        return cachedData;
      }

      // 如果是开发环境且后端未启动，返回模拟数据
      if (__DEV__ && error instanceof Error && error.message.includes('网络连接失败')) {
        console.log('🔄 Backend not available, using mock data');
        const article = MOCK_ARTICLES.find(article => article.id === id);

        if (!article) {
          throw new Error('文章不存在');
        }

        return {
          success: true,
          data: article,
          message: '使用模拟数据（后端服务不可用）',
        };
      }

      throw new Error(error instanceof Error ? error.message : '获取文章详情失败');
    }
  }

  // 获取分类列表
  async getCategories(): Promise<ApiResponse<NewsCategory[]>> {
    try {
      // 检查缓存
      const cacheKey = 'categories';
      const cachedData = await cacheManager.get<ApiResponse<NewsCategory[]>>(cacheKey);

      if (cachedData) {
        console.log('📦 Using cached categories data');
        return cachedData;
      }

      // 发送API请求
      const response = await this.client.get(API_ENDPOINTS.CATEGORIES);

      // 转换数据格式
      const transformedCategories = response.data.data.map(this.transformCategory);

      const result = {
        success: true,
        data: transformedCategories,
        message: response.data.message || '获取分类列表成功',
      };

      // 缓存结果
      await cacheManager.set(cacheKey, result, CACHE_CONFIG.CATEGORIES);

      return result;
    } catch (error) {
      console.error('获取分类列表失败:', error);

      // 尝试从缓存获取
      const cacheKey = 'categories';
      const cachedData = await cacheManager.get<ApiResponse<NewsCategory[]>>(cacheKey);

      if (cachedData) {
        console.log('📦 Using cached categories data (fallback)');
        return cachedData;
      }

      // 如果是开发环境且后端未启动，返回模拟数据
      if (__DEV__ && error instanceof Error && error.message.includes('网络连接失败')) {
        console.log('🔄 Backend not available, using mock data');
        return {
          success: true,
          data: NEWS_CATEGORIES,
          message: '使用模拟数据（后端服务不可用）',
        };
      }

      throw new Error(error instanceof Error ? error.message : '获取分类列表失败');
    }
  }

  // 搜索文章
  async searchArticles(
    query: string,
    page: number = 1,
    limit: number = 20
  ): Promise<ApiResponse<PaginatedResponse<NewsArticle>>> {
    try {
      // 构建查询参数
      const queryParams = new URLSearchParams();
      queryParams.append('q', query);
      queryParams.append('page', page.toString());
      queryParams.append('limit', limit.toString());

      // 检查缓存
      const cacheKey = `search_${queryParams.toString()}`;
      const cachedData = await cacheManager.get<ApiResponse<PaginatedResponse<NewsArticle>>>(cacheKey);

      if (cachedData) {
        console.log('📦 Using cached search data');
        return cachedData;
      }

      // 发送API请求
      const response = await this.client.get(`${API_ENDPOINTS.SEARCH}?${queryParams.toString()}`);

      // 转换数据格式
      const transformedData: PaginatedResponse<NewsArticle> = {
        items: response.data.data.items.map(this.transformNewsArticle),
        total: response.data.data.total,
        page: response.data.data.page,
        limit: response.data.data.limit,
        hasMore: response.data.data.hasMore,
      };

      const result = {
        success: true,
        data: transformedData,
        message: response.data.message || '搜索完成',
      };

      // 缓存结果
      await cacheManager.set(cacheKey, result, CACHE_CONFIG.SEARCH);

      return result;
    } catch (error) {
      console.error('搜索失败:', error);

      // 尝试从缓存获取
      const cacheKey = `search_${query}_${page}_${limit}`;
      const cachedData = await cacheManager.get<ApiResponse<PaginatedResponse<NewsArticle>>>(cacheKey);

      if (cachedData) {
        console.log('📦 Using cached search data (fallback)');
        return cachedData;
      }

      // 如果是开发环境且后端未启动，返回模拟数据
      if (__DEV__ && error instanceof Error && error.message.includes('网络连接失败')) {
        console.log('🔄 Backend not available, using mock search');
        return this.getMockSearchResults(query, page, limit);
      }

      throw new Error(error instanceof Error ? error.message : '搜索失败');
    }
  }

  // 获取热门文章
  async getTrendingArticles(limit: number = 10): Promise<ApiResponse<NewsArticle[]>> {
    try {
      // 检查缓存
      const cacheKey = `trending_${limit}`;
      const cachedData = await cacheManager.get<ApiResponse<NewsArticle[]>>(cacheKey);

      if (cachedData) {
        console.log('📦 Using cached trending data');
        return cachedData;
      }

      // 发送API请求
      const response = await this.client.get(`${API_ENDPOINTS.NEWS_TRENDING}?limit=${limit}`);

      // 转换数据格式
      const transformedArticles = response.data.data.map(this.transformNewsArticle);

      const result = {
        success: true,
        data: transformedArticles,
        message: response.data.message || '获取热门文章成功',
      };

      // 缓存结果
      await cacheManager.set(cacheKey, result, CACHE_CONFIG.TRENDING);

      return result;
    } catch (error) {
      console.error('获取热门文章失败:', error);

      // 如果是开发环境且后端未启动，返回模拟数据
      if (__DEV__ && error instanceof Error && error.message.includes('网络连接失败')) {
        console.log('🔄 Backend not available, using mock trending');
        const trendingArticles = MOCK_ARTICLES.slice(0, limit);
        return {
          success: true,
          data: trendingArticles,
          message: '使用模拟数据（后端服务不可用）',
        };
      }

      throw new Error(error instanceof Error ? error.message : '获取热门文章失败');
    }
  }

  // 获取推荐文章
  async getRecommendedArticles(
    articleId: string,
    limit: number = 5
  ): Promise<ApiResponse<NewsArticle[]>> {
    try {
      // 检查缓存
      const cacheKey = `recommended_${articleId}_${limit}`;
      const cachedData = await cacheManager.get<ApiResponse<NewsArticle[]>>(cacheKey);

      if (cachedData) {
        console.log('📦 Using cached recommended data');
        return cachedData;
      }

      // 发送API请求
      const response = await this.client.get(`${API_ENDPOINTS.NEWS_RECOMMENDED(articleId)}?limit=${limit}`);

      // 转换数据格式
      const transformedArticles = response.data.data.map(this.transformNewsArticle);

      const result = {
        success: true,
        data: transformedArticles,
        message: response.data.message || '获取推荐文章成功',
      };

      // 缓存结果
      await cacheManager.set(cacheKey, result, CACHE_CONFIG.RECOMMENDED);

      return result;
    } catch (error) {
      console.error('获取推荐文章失败:', error);

      // 如果是开发环境且后端未启动，返回模拟数据
      if (__DEV__ && error instanceof Error && error.message.includes('网络连接失败')) {
        console.log('🔄 Backend not available, using mock recommendations');
        const currentArticle = MOCK_ARTICLES.find(article => article.id === articleId);

        if (!currentArticle) {
          throw new Error('文章不存在');
        }

        // 模拟推荐逻辑：同分类的其他文章
        const recommendedArticles = MOCK_ARTICLES
          .filter(article =>
            article.id !== articleId &&
            article.category.id === currentArticle.category.id
          )
          .slice(0, limit);

        return {
          success: true,
          data: recommendedArticles,
          message: '使用模拟数据（后端服务不可用）',
        };
      }

      throw new Error(error instanceof Error ? error.message : '获取推荐文章失败');
    }
  }

  // 获取快讯列表
  async getFlashNews(
    page: number = 1,
    limit: number = 20
  ): Promise<ApiResponse<PaginatedResponse<any>>> {
    try {
      // 构建查询参数
      const queryParams = new URLSearchParams();
      queryParams.append('page', page.toString());
      queryParams.append('limit', limit.toString());

      // 检查缓存
      const cacheKey = `flash_${queryParams.toString()}`;
      const cachedData = await cacheManager.get<ApiResponse<PaginatedResponse<any>>>(cacheKey);

      if (cachedData) {
        console.log('📦 Using cached flash news data');
        return cachedData;
      }

      // 发送API请求
      const response = await this.client.get(`${API_ENDPOINTS.FLASH}?${queryParams.toString()}`);

      const result = {
        success: true,
        data: response.data.data,
        message: response.data.message || '获取快讯成功',
      };

      // 缓存结果
      await cacheManager.set(cacheKey, result, CACHE_CONFIG.FLASH);

      return result;
    } catch (error) {
      console.error('获取快讯失败:', error);

      // 如果是开发环境且后端未启动，返回模拟数据
      if (__DEV__ && error instanceof Error && error.message.includes('网络连接失败')) {
        console.log('🔄 Backend not available, using mock flash news');
        return {
          success: true,
          data: { items: [], total: 0, page, limit, hasMore: false },
          message: '使用模拟数据（后端服务不可用）',
        };
      }

      throw new Error(error instanceof Error ? error.message : '获取快讯失败');
    }
  }

  // 获取价格数据
  async getPrices(): Promise<ApiResponse<any[]>> {
    try {
      // 检查缓存
      const cacheKey = 'prices';
      const cachedData = await cacheManager.get<ApiResponse<any[]>>(cacheKey);

      if (cachedData) {
        console.log('📦 Using cached prices data');
        return cachedData;
      }

      // 发送API请求
      const response = await this.client.get(API_ENDPOINTS.PRICES);

      const result = {
        success: true,
        data: response.data.data,
        message: response.data.message || '获取价格数据成功',
      };

      // 缓存结果
      await cacheManager.set(cacheKey, result, CACHE_CONFIG.PRICES);

      return result;
    } catch (error) {
      console.error('获取价格数据失败:', error);

      // 如果是开发环境且后端未启动，返回模拟数据
      if (__DEV__ && error instanceof Error && error.message.includes('网络连接失败')) {
        console.log('🔄 Backend not available, using mock prices');
        return {
          success: true,
          data: [],
          message: '使用模拟数据（后端服务不可用）',
        };
      }

      throw new Error(error instanceof Error ? error.message : '获取价格数据失败');
    }
  }

  // 获取热门推特
  async getHotTweets(limit: number = 10): Promise<ApiResponse<any[]>> {
    try {
      // 检查缓存
      const cacheKey = `hot_tweets_${limit}`;
      const cachedData = await cacheManager.get<ApiResponse<any[]>>(cacheKey);

      if (cachedData) {
        console.log('📦 Using cached hot tweets data');
        return cachedData;
      }

      // 发送API请求
      const response = await this.client.get(`${API_ENDPOINTS.HOT_TWEETS}?limit=${limit}`);

      const result = {
        success: true,
        data: response.data.data,
        message: response.data.message || '获取热门推特成功',
      };

      // 缓存结果
      await cacheManager.set(cacheKey, result, CACHE_CONFIG.TRENDING);

      return result;
    } catch (error) {
      console.error('获取热门推特失败:', error);

      // 如果是开发环境且后端未启动，返回模拟数据
      if (__DEV__ && error instanceof Error && error.message.includes('网络连接失败')) {
        console.log('🔄 Backend not available, using mock tweets');
        return {
          success: true,
          data: [],
          message: '使用模拟数据（后端服务不可用）',
        };
      }

      throw new Error(error instanceof Error ? error.message : '获取热门推特失败');
    }
  }
}

// 创建API服务实例
export const apiService = new ApiService();

// 导出常用方法
export const {
  getArticles,
  getArticleById,
  getCategories,
  searchArticles,
  getTrendingArticles,
  getRecommendedArticles,
  getFlashNews,
  getPrices,
  getHotTweets,
} = apiService;
