import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  TouchableOpacity,
  Share,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useRoute, useNavigation, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';

import { RootStackParamList, NewsArticle } from '../../types';
import { useAppStore, useNewsStore } from '../../store';
import { COLORS, SPACING, FONT_SIZES, FONT_WEIGHTS, BORDER_RADIUS } from '../../constants';
import { useTheme } from '../../components/common/ThemeProvider';
import FavoriteButton from '../../components/ui/FavoriteButton';
import TouchableScale from '../../components/ui/TouchableScale';
import FontSizeController, { FontSize } from '../../components/ui/FontSizeController';
import ReadingProgressBar from '../../components/ui/ReadingProgressBar';
import ImageViewer from '../../components/ui/ImageViewer';
import RelatedArticles from '../../components/ui/RelatedArticles';
import ShareModal from '../../components/ui/ShareModal';

type ArticleDetailRouteProp = RouteProp<RootStackParamList, 'ArticleDetail'>;
type ArticleDetailNavigationProp = NativeStackNavigationProp<RootStackParamList, 'ArticleDetail'>;

const ArticleDetailScreen: React.FC = () => {
  const route = useRoute<ArticleDetailRouteProp>();
  const navigation = useNavigation<ArticleDetailNavigationProp>();
  const { articleId } = route.params;
  
  const { favorites, toggleFavorite } = useAppStore();
  const { getArticleById, articles } = useNewsStore();
  const { colors } = useTheme();

  const [article, setArticle] = useState<NewsArticle | null>(null);
  const [fontSize, setFontSize] = useState<FontSize>('medium');
  const [readingProgress, setReadingProgress] = useState(0);
  const [showFontController, setShowFontController] = useState(false);
  const [showImageViewer, setShowImageViewer] = useState(false);
  const [showShareModal, setShowShareModal] = useState(false);

  const styles = createStyles(colors, fontSize);

  useEffect(() => {
    const foundArticle = getArticleById(articleId);
    setArticle(foundArticle || null);
  }, [articleId]);

  const isFavorite = favorites.includes(articleId);

  const handleFavorite = () => {
    toggleFavorite(articleId);
  };

  const handleShare = () => {
    setShowShareModal(true);
  };

  const handleImagePress = () => {
    if (article?.imageUrl) {
      setShowImageViewer(true);
    }
  };

  const handleScroll = (event: any) => {
    const { contentOffset, contentSize, layoutMeasurement } = event.nativeEvent;
    const progress = contentOffset.y / (contentSize.height - layoutMeasurement.height);
    setReadingProgress(Math.max(0, Math.min(1, progress)));
  };

  // 获取相关文章
  const getRelatedArticles = () => {
    if (!article) return [];
    return articles
      .filter(a => a.id !== article.id && a.category.id === article.category.id)
      .slice(0, 5);
  };

  const getFontSizeValue = (size: FontSize) => {
    switch (size) {
      case 'small': return 14;
      case 'medium': return 16;
      case 'large': return 18;
      case 'extra-large': return 20;
      default: return 16;
    }
  };

  const renderHeader = () => (
    <View style={styles.header}>
      <TouchableScale
        style={styles.headerButton}
        onPress={() => navigation.goBack()}
      >
        <Ionicons name="arrow-back" size={24} color={colors.text} />
      </TouchableScale>

      <View style={styles.headerActions}>
        <TouchableScale
          style={styles.headerButton}
          onPress={() => setShowFontController(true)}
        >
          <Ionicons name="text-outline" size={24} color={colors.text} />
        </TouchableScale>

        <TouchableScale style={styles.headerButton} onPress={handleShare}>
          <Ionicons name="share-outline" size={24} color={colors.text} />
        </TouchableScale>

        <FavoriteButton articleId={articleId} />
      </View>
    </View>
  );

  if (!article) {
    return (
      <SafeAreaView style={styles.container}>
        {renderHeader()}
        <View style={styles.errorContainer}>
          <Ionicons name="document-text-outline" size={64} color={colors.textSecondary} />
          <Text style={styles.errorText}>文章未找到</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {renderHeader()}

      <ReadingProgressBar progress={readingProgress} />

      <ScrollView
        showsVerticalScrollIndicator={false}
        onScroll={handleScroll}
        scrollEventThrottle={16}
      >
        {article.imageUrl && (
          <TouchableScale onPress={handleImagePress}>
            <Image source={{ uri: article.imageUrl }} style={styles.heroImage} />
          </TouchableScale>
        )}

        <View style={styles.content}>
          <View style={styles.categoryBadge}>
            <Text style={styles.categoryText}>{article.category.name}</Text>
          </View>
          
          <Text style={styles.title}>{article.title}</Text>
          
          <View style={styles.meta}>
            <View style={styles.metaLeft}>
              <Text style={styles.author}>{article.author}</Text>
              <Text style={styles.publishedAt}>{article.publishedAt}</Text>
            </View>
            <View style={styles.metaRight}>
              <Text style={styles.readTime}>{article.readTime} 分钟阅读</Text>
              <View style={styles.progressIndicator}>
                <Text style={styles.progressText}>
                  {Math.round(readingProgress * 100)}%
                </Text>
              </View>
            </View>
          </View>
          
          <Text style={styles.summary}>{article.summary}</Text>
          
          <View style={styles.divider} />
          
          <Text style={styles.articleContent}>{article.content}</Text>
          
          {article.tags.length > 0 && (
            <View style={styles.tagsSection}>
              <Text style={styles.tagsTitle}>标签</Text>
              <View style={styles.tagsContainer}>
                {article.tags.map((tag, index) => (
                  <View key={index} style={styles.tag}>
                    <Text style={styles.tagText}>#{tag}</Text>
                  </View>
                ))}
              </View>
            </View>
          )}

          <RelatedArticles
            articles={getRelatedArticles()}
            onArticlePress={(id) => navigation.push('ArticleDetail', { articleId: id })}
          />
        </View>
      </ScrollView>
      
      <View style={styles.bottomActions}>
        <TouchableScale style={styles.actionButton} onPress={handleFavorite}>
          <Ionicons
            name={isFavorite ? 'heart' : 'heart-outline'}
            size={24}
            color={isFavorite ? colors.error : colors.textSecondary}
          />
          <Text style={styles.actionText}>
            {isFavorite ? '已收藏' : '收藏'}
          </Text>
        </TouchableScale>

        <TouchableScale style={styles.actionButton} onPress={handleShare}>
          <Ionicons name="share-outline" size={24} color={colors.textSecondary} />
          <Text style={styles.actionText}>分享</Text>
        </TouchableScale>
      </View>

      {/* 模态框组件 */}
      <FontSizeController
        visible={showFontController}
        onClose={() => setShowFontController(false)}
        currentSize={fontSize}
        onSizeChange={setFontSize}
      />

      <ImageViewer
        visible={showImageViewer}
        imageUrl={article?.imageUrl || ''}
        title={article?.title}
        onClose={() => setShowImageViewer(false)}
      />

      <ShareModal
        visible={showShareModal}
        onClose={() => setShowShareModal(false)}
        title={article?.title || ''}
        url={`https://app.blockchainnews.com/article/${article?.id}`}
        content={article?.summary || ''}
      />
    </SafeAreaView>
  );
};

const createStyles = (colors: any, fontSize: FontSize = 'medium') =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: SPACING.md,
      paddingVertical: SPACING.sm,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    headerButton: {
      padding: SPACING.sm,
    },
    headerActions: {
      flexDirection: 'row',
    },
    heroImage: {
      width: '100%',
      height: 250,
    },
    content: {
      paddingHorizontal: SPACING.md,
      paddingTop: SPACING.sm, // 减少顶部留白
      paddingBottom: SPACING.xl, // 保持底部足够间距
    },
    categoryBadge: {
      alignSelf: 'flex-start',
      backgroundColor: colors.primary,
      paddingHorizontal: SPACING.sm,
      paddingVertical: SPACING.xs,
      borderRadius: 16,
      marginBottom: SPACING.sm, // 减少与标题的间距
    },
    categoryText: {
      fontSize: FONT_SIZES.sm,
      color: 'white',
      fontWeight: '500',
    },
    title: {
      fontSize: FONT_SIZES['3xl'],
      fontWeight: 'bold',
      color: colors.text,
      lineHeight: 34, // 稍微减少行高，提升密度
      marginBottom: SPACING.sm, // 减少与meta信息的间距
    },
    meta: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: SPACING.sm, // 减少与摘要的间距
    },
    metaLeft: {
      flex: 1,
    },
    metaRight: {
      alignItems: 'flex-end',
    },
    progressIndicator: {
      backgroundColor: colors.primary + '20',
      paddingHorizontal: SPACING.sm,
      paddingVertical: SPACING.xs,
      borderRadius: BORDER_RADIUS.sm,
      marginTop: SPACING.xs,
    },
    progressText: {
      fontSize: FONT_SIZES.xs,
      color: colors.primary,
      fontWeight: FONT_WEIGHTS.semibold,
    },
    author: {
      fontSize: FONT_SIZES.md,
      fontWeight: '600',
      color: colors.text,
      marginBottom: SPACING.xs,
    },
    publishedAt: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
    },
    readTime: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
    },
    summary: {
      fontSize: FONT_SIZES.lg,
      color: colors.textSecondary,
      lineHeight: 22, // 稍微减少行高
      marginBottom: SPACING.md, // 减少与分割线的间距
      fontStyle: 'italic',
    },
    divider: {
      height: 1,
      backgroundColor: colors.border,
      marginVertical: SPACING.md, // 减少分割线上下间距
    },
    articleContent: {
      fontSize: (() => {
        switch (fontSize) {
          case 'small': return 14;
          case 'medium': return 16;
          case 'large': return 18;
          case 'extra-large': return 20;
          default: return 16;
        }
      })(),
      color: colors.text,
      lineHeight: (() => {
        switch (fontSize) {
          case 'small': return 20;
          case 'medium': return 24;
          case 'large': return 26;
          case 'extra-large': return 28;
          default: return 24;
        }
      })(),
      marginBottom: SPACING.md, // 减少文章内容的底部间距
    },
    tagsSection: {
      marginTop: SPACING.lg,
    },
    tagsTitle: {
      fontSize: FONT_SIZES.md,
      fontWeight: '600',
      color: colors.text,
      marginBottom: SPACING.sm,
    },
    tagsContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
    },
    tag: {
      backgroundColor: colors.surface,
      paddingHorizontal: SPACING.sm,
      paddingVertical: SPACING.xs,
      borderRadius: 16,
      marginRight: SPACING.sm,
      marginBottom: SPACING.sm,
    },
    tagText: {
      fontSize: FONT_SIZES.sm,
      color: colors.primary,
      fontWeight: '500',
    },
    bottomActions: {
      flexDirection: 'row',
      backgroundColor: colors.surface,
      paddingHorizontal: SPACING.md,
      paddingVertical: SPACING.sm,
      borderTopWidth: 1,
      borderTopColor: colors.border,
    },
    actionButton: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: SPACING.sm,
    },
    actionText: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      marginLeft: SPACING.xs,
    },
    errorContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    errorText: {
      fontSize: FONT_SIZES.lg,
      color: colors.textSecondary,
      marginTop: SPACING.md,
    },
  });

export default ArticleDetailScreen;
