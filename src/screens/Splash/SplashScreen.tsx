import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  Dimensions,
  StatusBar,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../components/common/ThemeProvider';
import { SPACING, FONT_SIZES, FONT_WEIGHTS, SPRING_CONFIG, SPLASH_TIME } from '../../constants';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface SplashScreenProps {
  onFinish: () => void;
}

const SplashScreen: React.FC<SplashScreenProps> = ({ onFinish }) => {
  const { colors, theme } = useTheme();
  const styles = createStyles(colors);

  // 动画值
  const logoScale = useRef(new Animated.Value(0.3)).current;
  const logoOpacity = useRef(new Animated.Value(0)).current;
  const titleOpacity = useRef(new Animated.Value(0)).current;
  const subtitleOpacity = useRef(new Animated.Value(0)).current;
  const blockchainAnim = useRef(new Animated.Value(0)).current;
  const loadingDots = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    startAnimation();
  }, []);

  const startAnimation = () => {
    // 启动动画序列
    Animated.sequence([
      // Logo 出现
      Animated.parallel([
        Animated.timing(logoOpacity, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.spring(logoScale, {
          toValue: 1,
          tension: SPRING_CONFIG.tension,
          friction: 8,
          useNativeDriver: true,
        }),
      ]),
      
      // 标题出现
      Animated.timing(titleOpacity, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      
      // 副标题出现
      Animated.timing(subtitleOpacity, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      
      // 区块链动画
      Animated.timing(blockchainAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
    ]).start();

    // 加载点动画（循环）
    Animated.loop(
      Animated.sequence([
        Animated.timing(loadingDots, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(loadingDots, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    ).start();

    // SPLASH_TIME秒后完成启动
    setTimeout(() => {
      onFinish();
    }, SPLASH_TIME);
  };

  const renderBlockchainAnimation = () => {
    const blocks = Array.from({ length: 5 }, (_, index) => {
      const translateX = blockchainAnim.interpolate({
        inputRange: [0, 1],
        outputRange: [screenWidth, index * 60 - 120],
      });
      const randomIcon = () => {
        const icons = `accessibility airplane alarm analytics aperture apps at-circle balloon bar-chart  book-outline build-outline`.split(/\s+/).filter(Boolean)
        return icons[Math.floor(Math.random() * icons.length)];
      }
      return (
        <Animated.View
          key={index}
          style={[
            styles.blockchainBlock,
            {
              transform: [{ translateX }],
              opacity: blockchainAnim,
            },
          ]}
        >
          <Ionicons
            name={randomIcon() as any}
            size={24}
            color={colors.primary}
          />
        </Animated.View>
      );
    });

    return <View style={styles.blockchainContainer}>{blocks}</View>;
  };

  const renderLoadingDots = () => {
    const dots = Array.from({ length: 3 }, (_, index) => {
      const opacity = loadingDots.interpolate({
        inputRange: [0, 0.5, 1],
        outputRange: [0.3, 1, 0.3],
        extrapolate: 'clamp',
      });

      const scale = loadingDots.interpolate({
        inputRange: [0, 0.5, 1],
        outputRange: [0.8, 1.2, 0.8],
        extrapolate: 'clamp',
      });

      return (
        <Animated.View
          key={index}
          style={[
            styles.loadingDot,
            {
              opacity,
              transform: [{ scale }],
            },
          ]}
        />
      );
    });

    return <View style={styles.loadingContainer}>{dots}</View>;
  };

  return (
    <View style={styles.container}>
      <StatusBar
        barStyle={theme === 'dark' ? 'light-content' : 'dark-content'}
        backgroundColor={colors.background}
      />
      
      {/* 背景装饰 */}
      <View style={styles.backgroundDecoration}>
        <View style={[styles.circle, styles.circle1]} />
        <View style={[styles.circle, styles.circle2]} />
        <View style={[styles.circle, styles.circle3]} />
      </View>

      {/* 主要内容 */}
      <View style={styles.content}>
        {/* Logo */}
        <Animated.View
          style={[
            styles.logoContainer,
            {
              opacity: logoOpacity,
              transform: [{ scale: logoScale }],
            },
          ]}
        >
          <View style={styles.logo}>
            <Ionicons
              name="diamond"
              size={64}
              color={colors.primary}
            />
          </View>
        </Animated.View>

        {/* 标题 */}
        <Animated.View
          style={[
            styles.titleContainer,
            { opacity: titleOpacity },
          ]}
        >
          <Text style={styles.title}>ChainMix</Text>
        </Animated.View>

        {/* 副标题 */}
        <Animated.View
          style={[
            styles.subtitleContainer,
            { opacity: subtitleOpacity },
          ]}
        >
          <Text style={styles.subtitle}>一个智能的区块链聚合平台</Text>
        </Animated.View>

        {/* 区块链动画 */}
        {renderBlockchainAnimation()}
      </View>

      {/* 底部加载 */}
      <View style={styles.footer}>
        {renderLoadingDots()}
        <Text style={styles.loadingText}>loading...</Text>
      </View>
    </View>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    backgroundDecoration: {
      position: 'absolute',
      width: '100%',
      height: '100%',
    },
    circle: {
      position: 'absolute',
      borderRadius: 1000,
      opacity: 0.1,
    },
    circle1: {
      width: 200,
      height: 200,
      backgroundColor: colors.primary,
      top: -100,
      right: -100,
    },
    circle2: {
      width: 150,
      height: 150,
      backgroundColor: colors.secondary,
      bottom: 100,
      left: -75,
    },
    circle3: {
      width: 100,
      height: 100,
      backgroundColor: colors.accent,
      top: '40%',
      right: 50,
    },
    content: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: SPACING.xl,
    },
    logoContainer: {
      marginBottom: SPACING.xl,
    },
    logo: {
      width: 120,
      height: 120,
      borderRadius: 60,
      backgroundColor: colors.surface,
      justifyContent: 'center',
      alignItems: 'center',
      shadowColor: colors.shadow,
      shadowOffset: { width: 0, height: 8 },
      shadowOpacity: 0.2,
      shadowRadius: 16,
      elevation: 8,
    },
    titleContainer: {
      marginBottom: SPACING.md,
    },
    title: {
      fontSize: FONT_SIZES['4xl'],
      fontWeight: FONT_WEIGHTS.black,
      color: colors.text,
      textAlign: 'center',
      letterSpacing: -1,
    },
    subtitleContainer: {
      marginBottom: SPACING['2xl'],
    },
    subtitle: {
      fontSize: FONT_SIZES.lg,
      color: colors.textSecondary,
      textAlign: 'center',
      fontWeight: FONT_WEIGHTS.medium,
    },
    blockchainContainer: {
      flexDirection: 'row',
      height: 60,
      width: screenWidth,
      justifyContent: 'center',
      alignItems: 'center',
    },
    blockchainBlock: {
      width: 48,
      height: 48,
      backgroundColor: colors.surface,
      borderRadius: 8,
      justifyContent: 'center',
      alignItems: 'center',
      marginHorizontal: 4,
      shadowColor: colors.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 2,
    },
    footer: {
      paddingBottom: SPACING['3xl'],
      alignItems: 'center',
    },
    loadingContainer: {
      flexDirection: 'row',
      marginBottom: SPACING.md,
    },
    loadingDot: {
      width: 8,
      height: 8,
      borderRadius: 4,
      backgroundColor: colors.primary,
      marginHorizontal: 4,
    },
    loadingText: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      fontWeight: FONT_WEIGHTS.medium,
    },
  });

export default SplashScreen;
