import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { AppState, NewsState, User, NewsArticle, NewsCategory } from '../types';
import { NEWS_CATEGORIES } from '../constants';
import { apiService } from '../services/api';
import { MOCK_ARTICLES } from '../services/mockData';

// 应用状态 Store
interface AppStore extends AppState {
  setTheme: (theme: 'light' | 'dark') => void;
  setLoading: (isLoading: boolean) => void;
  setUser: (user: User | null) => void;
  toggleFavorite: (articleId: string) => void;
  clearFavorites: () => void;
}

export const useAppStore = create<AppStore>()(
  persist(
    (set, get) => ({
      theme: 'light',
      isLoading: false,
      user: null,
      favorites: [],
      
      setTheme: (theme) => set({ theme }),
      setLoading: (isLoading) => set({ isLoading }),
      setUser: (user) => set({ user }),
      
      toggleFavorite: (articleId) => {
        const { favorites } = get();
        const isFavorite = favorites.includes(articleId);
        
        if (isFavorite) {
          set({ favorites: favorites.filter(id => id !== articleId) });
        } else {
          set({ favorites: [...favorites, articleId] });
        }
      },
      
      clearFavorites: () => set({ favorites: [] }),
    }),
    {
      name: 'app-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        theme: state.theme,
        user: state.user,
        favorites: state.favorites,
      }),
    }
  )
);

// 新闻状态 Store
interface NewsStore extends NewsState {
  setArticles: (articles: NewsArticle[]) => void;
  addArticles: (articles: NewsArticle[]) => void;
  setCategories: (categories: NewsCategory[]) => void;
  setLoading: (isLoading: boolean) => void;
  setError: (error: string | null) => void;
  setCurrentPage: (page: number) => void;
  setHasMore: (hasMore: boolean) => void;
  clearArticles: () => void;
  getArticleById: (id: string) => NewsArticle | undefined;
  getArticlesByCategory: (categoryId: string) => NewsArticle[];
  loadArticles: (page?: number, category?: string) => Promise<void>;
  loadMoreArticles: (category?: string) => Promise<void>;
  refreshArticles: (category?: string) => Promise<void>;
  searchArticles: (query: string) => Promise<void>;
}

export const useNewsStore = create<NewsStore>((set, get) => ({
  articles: MOCK_ARTICLES, // 初始化时加载模拟数据
  categories: NEWS_CATEGORIES,
  isLoading: false,
  error: null,
  currentPage: 1,
  hasMore: true,

  setArticles: (articles) => set({ articles }),
  addArticles: (articles) => {
    const { articles: currentArticles } = get();
    const newArticles = articles.filter(
      article => !currentArticles.some(existing => existing.id === article.id)
    );
    set({ articles: [...currentArticles, ...newArticles] });
  },

  setCategories: (categories) => set({ categories }),
  setLoading: (isLoading) => set({ isLoading }),
  setError: (error) => set({ error }),
  setCurrentPage: (page) => set({ currentPage: page }),
  setHasMore: (hasMore) => set({ hasMore }),
  clearArticles: () => set({ articles: [], currentPage: 1, hasMore: true }),

  getArticleById: (id) => {
    const { articles } = get();
    return articles.find(article => article.id === id);
  },

  getArticlesByCategory: (categoryId) => {
    const { articles } = get();
    return articles.filter(article => article.category.id === categoryId);
  },

  // 加载文章
  loadArticles: async (page = 1, category) => {
    const { setLoading, setError, setArticles, setCurrentPage, setHasMore } = get();

    try {
      setLoading(true);
      setError(null);

      const response = await apiService.getArticles(page, 20, category);

      if (response.success) {
        setArticles(response.data.items);
        setCurrentPage(response.data.page);
        setHasMore(response.data.hasMore);
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : '加载失败');
    } finally {
      setLoading(false);
    }
  },

  // 加载更多文章
  loadMoreArticles: async (category) => {
    const { currentPage, hasMore, isLoading, setLoading, setError, addArticles, setCurrentPage, setHasMore } = get();

    if (!hasMore || isLoading) return;

    try {
      setLoading(true);
      setError(null);

      const nextPage = currentPage + 1;
      const response = await apiService.getArticles(nextPage, 20, category);

      if (response.success) {
        addArticles(response.data.items);
        setCurrentPage(response.data.page);
        setHasMore(response.data.hasMore);
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : '加载失败');
    } finally {
      setLoading(false);
    }
  },

  // 刷新文章
  refreshArticles: async (category) => {
    const { clearArticles, loadArticles } = get();
    clearArticles();
    await loadArticles(1, category);
  },

  // 搜索文章
  searchArticles: async (query) => {
    const { setLoading, setError, setArticles, setCurrentPage, setHasMore } = get();

    try {
      setLoading(true);
      setError(null);

      const response = await apiService.searchArticles(query, 1, 20);

      if (response.success) {
        setArticles(response.data.items);
        setCurrentPage(response.data.page);
        setHasMore(response.data.hasMore);
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : '搜索失败');
    } finally {
      setLoading(false);
    }
  },
}));
