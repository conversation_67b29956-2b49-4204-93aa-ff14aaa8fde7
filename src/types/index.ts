// 基础类型定义
export interface NewsArticle {
  id: string;
  title: string;
  summary: string;
  content: string;
  imageUrl?: string;
  category: NewsCategory;
  publishedAt: string;
  author: string;
  readTime: number; // 阅读时间（分钟）
  tags: string[];
  isFavorite?: boolean; // 前端特有字段
  // 后端额外字段（可选）
  source?: {
    name: string;
    url: string;
  };
  engagement?: {
    views: number;
    likes: number;
    shares: number;
  };
}

export interface NewsCategory {
  id: string;
  name: string;
  slug: string;
  color: string;
  icon: string;
}

export interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  preferences: UserPreferences;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  language: string;
  notifications: boolean;
  favoriteCategories: string[];
}

// 导航类型
export type RootStackParamList = {
  Main: undefined;
  ArticleDetail: { articleId: string };
  FeaturedList: undefined;
  TwitterList: undefined;
  TrendingList: undefined;
};

export type MainTabParamList = {
  Home: undefined;
  News: undefined;
  AI: undefined;
  Discover: undefined;
  Profile: undefined;
};

// API 响应类型
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
  timestamp?: string; // 后端额外字段
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
  totalPages?: number; // 后端额外字段
}

// 主题类型
export type ThemeMode = 'light' | 'dark';

// 状态类型
export interface AppState {
  theme: ThemeMode;
  isLoading: boolean;
  user: User | null;
  favorites: string[];
}

export interface NewsState {
  articles: NewsArticle[];
  categories: NewsCategory[];
  isLoading: boolean;
  error: string | null;
  currentPage: number;
  hasMore: boolean;
}

// 组件Props类型
export interface BaseComponentProps {
  style?: any;
  testID?: string;
}

export interface TouchableComponentProps extends BaseComponentProps {
  onPress?: () => void;
  disabled?: boolean;
}

// 错误类型
export interface AppError {
  code: string;
  message: string;
  details?: any;
}

// 加载状态类型
export type LoadingState = 'idle' | 'loading' | 'success' | 'error';

// 搜索相关类型
export interface SearchState {
  query: string;
  results: NewsArticle[];
  isLoading: boolean;
  error: string | null;
}
