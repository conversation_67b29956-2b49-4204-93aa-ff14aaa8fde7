import AsyncStorage from '@react-native-async-storage/async-storage';

interface CacheItem<T> {
  data: T;
  timestamp: number;
  expiry: number;
}

class CacheManager {
  private static instance: CacheManager;
  private cache: Map<string, CacheItem<any>> = new Map();

  static getInstance(): CacheManager {
    if (!CacheManager.instance) {
      CacheManager.instance = new CacheManager();
    }
    return CacheManager.instance;
  }

  async set<T>(key: string, data: T, ttl: number = 5 * 60 * 1000): Promise<void> {
    const item: CacheItem<T> = {
      data,
      timestamp: Date.now(),
      expiry: Date.now() + ttl,
    };

    // 内存缓存
    this.cache.set(key, item);

    // 持久化缓存
    try {
      await AsyncStorage.setItem(`cache_${key}`, JSON.stringify(item));
    } catch (error) {
      console.warn('Failed to save to persistent cache:', error);
    }
  }

  async get<T>(key: string): Promise<T | null> {
    // 先检查内存缓存
    const memoryItem = this.cache.get(key);
    if (memoryItem && memoryItem.expiry > Date.now()) {
      return memoryItem.data;
    }

    // 检查持久化缓存
    try {
      const persistentItem = await AsyncStorage.getItem(`cache_${key}`);
      if (persistentItem) {
        const item: CacheItem<T> = JSON.parse(persistentItem);
        if (item.expiry > Date.now()) {
          // 恢复到内存缓存
          this.cache.set(key, item);
          return item.data;
        } else {
          // 过期了，删除
          await this.remove(key);
        }
      }
    } catch (error) {
      console.warn('Failed to read from persistent cache:', error);
    }

    return null;
  }

  async remove(key: string): Promise<void> {
    this.cache.delete(key);
    try {
      await AsyncStorage.removeItem(`cache_${key}`);
    } catch (error) {
      console.warn('Failed to remove from persistent cache:', error);
    }
  }

  async clear(): Promise<void> {
    this.cache.clear();
    try {
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(key => key.startsWith('cache_'));
      await AsyncStorage.multiRemove(cacheKeys);
    } catch (error) {
      console.warn('Failed to clear persistent cache:', error);
    }
  }

  // 清理过期的缓存项
  cleanup(): void {
    const now = Date.now();
    for (const [key, item] of this.cache.entries()) {
      if (item.expiry <= now) {
        this.cache.delete(key);
        AsyncStorage.removeItem(`cache_${key}`).catch(() => {});
      }
    }
  }

  // 获取缓存统计信息
  getStats(): { memorySize: number; keys: string[] } {
    return {
      memorySize: this.cache.size,
      keys: Array.from(this.cache.keys()),
    };
  }
}

export const cacheManager = CacheManager.getInstance();

// 定期清理过期缓存
setInterval(() => {
  cacheManager.cleanup();
}, 10 * 60 * 1000); // 每10分钟清理一次
